import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ApiRequest, ApiResponse } from 'app/dto/common/commonDto';
import { Observable } from 'rxjs';
import { BaseApiServiceService } from './baseapi.service';


@Injectable({
  providedIn: 'root'
})

export class EmailApiService extends BaseApiServiceService {

  constructor(public http: HttpClient, public router: Router) {
    super();
  }

  getEmails(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}emaillist`,
        {
          'data': request
        });
  }

  saveEmails(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}saveemails`,
        {
          'data': request
        });
  }  

}

import { Moment } from "moment";
export interface SaveStaffRequestDto {
    staffId: number;
    facilityId: number;
    employeeId: string;
    employeeName: string;
    emailAddress: string;
    contactNumber: string;
    roleId: number;
    dateOfJoining: Moment;
    nationality: string;
    postalCode:number;
    photoPath: string;
    photo: string;
    gender: string;
    isProfileLocked: boolean;
    isActive: boolean;
    resignationDate: Date;
    createdBy: number;
} 
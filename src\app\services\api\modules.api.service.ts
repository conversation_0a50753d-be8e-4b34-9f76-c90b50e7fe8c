import { Injectable } from '@angular/core';
import { BaseApiServiceService } from './baseapi.service';
import { Router } from '@angular/router';
import { ApiResponse, ApiRequest } from 'app/dto/common/commonDto';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ModuleApiService extends BaseApiServiceService {

  constructor(public http: HttpClient, public router: Router) {
    super();
  }

  getModules(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}getallmodules`,
      {
        'data': request
      });
  }
  
  getUserModules(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}modules`,
        {
          'data': request
        });
  }
}

import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { FuseSharedModule } from '@fuse/shared.module';
import { LoginComponent } from 'app/main/login/login.component';
// import { MsalModule } from '@azure/msal-angular';
import { SocialLoginModule, AuthServiceConfig } from 'angularx-social-login';
import { GoogleLoginProvider, FacebookLoginProvider } from 'angularx-social-login';
 
 
// const config = new AuthServiceConfig([
//   {
//     id: GoogleLoginProvider.PROVIDER_ID,
//     provider: new GoogleLoginProvider('176275011871-v21m73jh0q9caubudrsdgd69q9o6hrh1.apps.googleusercontent.com')
//   }
// ]);
 
// export function provideConfig() {
//   return config;
// }


const routes = [
    {
        path     : 'login',
        component: LoginComponent
    }
];
const isIE = window.navigator.userAgent.indexOf('MSIE ') > -1 || window.navigator.userAgent.indexOf('Trident/') > -1;

@NgModule({
    declarations: [
        LoginComponent
    ],
    imports     : [
        RouterModule.forChild(routes),

        MatButtonModule,
        MatCheckboxModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
       
        FuseSharedModule,
      //  MsalModule,
        SocialLoginModule
    ],
    providers: [
      //  {
          // provide: AuthServiceConfig,
          // useFactory: provideConfig
        //}
      ]
})
export class LoginModule
{
}

import { Component, On<PERSON><PERSON>roy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { delay, filter, take, takeUntil } from 'rxjs/operators';

import { FuseConfigService } from '@fuse/services/config.service';
import { FuseNavigationService } from '@fuse/components/navigation/navigation.service';
import { FusePerfectScrollbarDirective } from '@fuse/directives/fuse-perfect-scrollbar/fuse-perfect-scrollbar.directive';
import { FuseSidebarService } from '@fuse/components/sidebar/sidebar.service';
import { MatDialog } from '@angular/material';
import { changepasswordComponent } from 'app/main/common/changepassword/changepassword';

@Component({
    selector: 'navbar-vertical-style-1',
    templateUrl: './style-1.component.html',
    styleUrls: ['./style-1.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class NavbarVerticalStyle1Component implements OnInit, OnDestroy {
    fuseConfig: any;
    navigation: any;
    userName: string;
    picUrl: string;
    userEmail: string;
    showImage: boolean = false;

    // Private
    private _fusePerfectScrollbar: FusePerfectScrollbarDirective;
    private _unsubscribeAll: Subject<any>;

    /**
     * Constructor
     *
     * @param {FuseConfigService} _fuseConfigService
     * @param {FuseNavigationService} _fuseNavigationService
     * @param {FuseSidebarService} _fuseSidebarService
     * @param {Router} _router
     */
    constructor(
        private _fuseConfigService: FuseConfigService,
        private _fuseNavigationService: FuseNavigationService,
        private _fuseSidebarService: FuseSidebarService,
        private _router: Router,
        private _dialog: MatDialog,
    ) {
        // Set the private defaults
        this._unsubscribeAll = new Subject();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    // Directive
    @ViewChild(FusePerfectScrollbarDirective, { static: true })
    set directive(theDirective: FusePerfectScrollbarDirective) {
        if (!theDirective) {
            return;
        }

        this._fusePerfectScrollbar = theDirective;

        // Update the scrollbar on collapsable item toggle
        this._fuseNavigationService.onItemCollapseToggled
            .pipe(
                delay(500),
                takeUntil(this._unsubscribeAll)
            )
            .subscribe(() => {
                this._fusePerfectScrollbar.update();
            });

        // Scroll to the active item position
        this._router.events
            .pipe(
                filter((event) => event instanceof NavigationEnd),
                take(1)
            )
            .subscribe(() => {
                setTimeout(() => {
                    this._fusePerfectScrollbar.scrollToElement('navbar .nav-link.active', -120);
                });
            }
            );
    }


    ngOnInit(): void {
        this._router.events
            .pipe(
                filter((event) => event instanceof NavigationEnd),
                takeUntil(this._unsubscribeAll)
            )
            .subscribe(() => {
                if (this._fuseSidebarService.getSidebar('navbar')) {
                    this._fuseSidebarService.getSidebar('navbar').close();
                }
            }
            );

        // Subscribe to the config changes
        this._fuseConfigService.config
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((config) => {
                this.fuseConfig = config;
            });

        // Get current navigation
        this._fuseNavigationService.onNavigationChanged
            .pipe(
                filter(value => value !== null),
                takeUntil(this._unsubscribeAll)
            )
            .subscribe(() => {
                this.navigation = this._fuseNavigationService.getCurrentNavigation();
            });

        this.userName = localStorage.getItem('SSOUSERNAME');
        this.picUrl = localStorage.getItem('SSOUSERPIC');
        this.userEmail = localStorage.getItem('SSOUSEREMAIL');

        // Check if we should show image initially
        this.showImage = !!(this.picUrl && this.picUrl.trim());
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next();
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Toggle sidebar opened status
     */
    // toggleSidebarOpened(): void
    // {
    //     this._fuseSidebarService.getSidebar('navbar').toggleOpen();
    // }

    // /**
    //  * Toggle sidebar folded status
    //  */
    // toggleSidebarFolded(): void
    // {
    //     this._fuseSidebarService.getSidebar('navbar').toggleFold();
    // }

    logout(): void {

        localStorage.setItem('SSOADMIN', '');
        localStorage.setItem('SSOTOKEN', '');
        localStorage.setItem('SSOUSERNAME', '');
        localStorage.setItem('SSOUSERPIC', '');
        localStorage.setItem('SSOUSEREMAIL', '');
        localStorage.setItem('SSOUSERCOMPANYID', '');
        this._router.navigateByUrl('/logout?action=logout');
    }

    changepassword(): boolean {
        const dialogRef = this._dialog.open(changepasswordComponent, {
            width: '400px',
            data: {
                name: 'Change Password',
                filePath: '',
                base64: ''
            }
        });
        return false;
    }

    /**
     * Handle image load error - show initials instead
     */
    onImageError(): void {
        this.showImage = false;
    }

    /**
     * Handle successful image load
     */
    onImageLoad(): void {
        this.showImage = true;
    }

    /**
     * Get initials from user name (first letter of first name and last name)
     */
    getInitials(): string {
        if (!this.userName) {
            return '??';
        }

        const names = this.userName.trim().split(' ').filter(name => name.length > 0);

        if (names.length === 0) {
            return '??';
        } else if (names.length === 1) {
            // Only one name, take first two characters
            return names[0].substring(0, 2).toUpperCase();
        } else {
            // Take first letter of first name and first letter of last name
            const firstInitial = names[0].charAt(0);
            const lastInitial = names[names.length - 1].charAt(0);
            return (firstInitial + lastInitial).toUpperCase();
        }
    }
}

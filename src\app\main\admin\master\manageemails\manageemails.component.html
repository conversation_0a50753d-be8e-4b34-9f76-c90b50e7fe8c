<div id="forms" class="page-layout simple fullwidth" fxLayout="column">
    <div class="header accent p-24 h-20" fxLayout="row" fxLayoutAlign="start center">
        <div fxLayout="column" fxLayoutAlign="center start">
            <div fxLayout="row" fxLayoutAlign="start center">
                <h2>Manage Superadministrators</h2>
            </div>
        </div>
    </div>
    <div class="content p-24">
        <div class="mb-24" fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="space-around start" fxLayout.gt-md="row">
            <div fxFlex="100" fxLayout="column">
                <div fxFlex="100" fxLayout="column" class="mt-20">
                    <div fxLayout="row wrap" fxLayoutGap="10px" fxLayoutAlign="start" fxLayout.gt-md="row wrap">
                        <div fxFlex="40" fxLayout="column" style="align-items: center">
                            <form class="mat-card mat-elevation-z4 p-24" fxLayout="column" fxLayoutAlign="start"
                                [formGroup]="fgEmail" fxFlex="1 0 shrink" name="form" (ngSubmit)="saveUser()"
                                #formUser="ngForm">
                                <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                                    <mat-form-field appearance="outline" fxFlex="100" class="pr-4">
                                        <mat-label>Email</mat-label>
                                        <input matInput required type="email" formControlName="email">
                                        <mat-icon matSuffix class="secondary-text">email</mat-icon>
                                        <mat-error>Email is required!</mat-error>
                                    </mat-form-field>
                                </div>
                                <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                                    <div fxLayout="row wrap" fxFlex="100" fxLayoutAlign="start center">
                                        <mat-checkbox fxFlex="50" formControlName="active">Active</mat-checkbox>
                                    </div>
                                </div>
                                <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                                    <div fxLayout="row" fxLayoutAlign="end center" fxFlex="1 0 auto">
                                        <button mat-stroked-button type="reset" (click)="resetExtraDetails()"
                                            class="mat-top-margin mr-20" color="primary">Reset</button>
                                        <button mat-flat-button type="submit" [disabled]="fgEmail.invalid"
                                            class="mat-top-margin mr-20" color="primary">Save
                                            Changes</button>
                                        <!-- <button mat-flat-button type="button" class="mat-top-margin" color="primary">Review &
                                        Save Changes</button> -->
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div fxFlex="50" fxLayout="column">
                            <div class="mat-card mat-elevation-z4 p-24">
                                <mat-form-field style="width:50%;">
                                    <button mat-button matPrefix mat-icon-button>
                                        <mat-icon>search</mat-icon>
                                    </button>
                                    <input matInput type="text" (keyup)="getAllEmails()" [(ngModel)]="search_user"
                                        placeholder="Filter Emails">
                                    <button *ngIf="search_user" mat-button matSuffix mat-icon-button aria-label="Clear"
                                        (click)="search_user=''; getAllEmails();">
                                        <mat-icon>close</mat-icon>
                                    </button>
                                </mat-form-field>
                                <table mat-table [dataSource]="dataSource" matSort>
                                    <ng-container matColumnDef="email">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Email </th>
                                        <td mat-cell *matCellDef="let row"> {{row.email}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="isActive">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Active </th>
                                        <td mat-cell *matCellDef="let row"> {{row.isActive === true? 'Yes': 'No'}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="edit">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Edit </th>
                                        <td mat-cell *matCellDef="let row" [style.color]="">
                                            <button mat-stroked-button color="primary" (click)="loadEmailDetails(row)"
                                                matSuffix class="mat-sm">
                                                <mat-icon>edit</mat-icon>
                                            </button>
                                        </td>
                                    </ng-container>
                                    <ng-container matColumnDef="delete">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Delete </th>
                                        <td mat-cell *matCellDef="let row" [style.color]="">
                                            <button mat-stroked-button color="primary" (click)="deleteEmailDetails(row)"
                                                matSuffix class="mat-sm">
                                                <mat-icon>delete</mat-icon>
                                            </button>
                                        </td>
                                    </ng-container>
                                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                                        [ngClass]="{'highlight': selectedRowIndex == row.emailId}">
                                    </tr>
                                </table>
                                <div *ngIf="!dataSource?.data?.length && search_user" class="noRecordsMessage">
                                    <mat-icon>search_off</mat-icon>
                                    No matching records found. Please try a different search.
                                </div>
                                <mat-paginator [length]="length" [pageSize]="pageSize"
                                    [pageSizeOptions]="pageSizeOptions" (page)="pagerEvent($event)"></mat-paginator>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mb-24" fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="space-around start" fxLayout.gt-md="row">
        </div>
    </div>
</div>
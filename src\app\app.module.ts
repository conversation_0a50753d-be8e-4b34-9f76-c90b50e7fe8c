import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { HttpClientModule } from '@angular/common/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule, Routes } from '@angular/router';
import { MatMomentDateModule } from '@angular/material-moment-adapter';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import 'hammerjs';

import { FuseModule } from '@fuse/fuse.module';
import { FuseSharedModule } from '@fuse/shared.module';
import { FuseProgressBarModule, FuseSidebarModule, FuseThemeOptionsModule } from '@fuse/components';
import { AngularFontAwesomeModule } from 'angular-font-awesome';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { fuseConfig } from 'app/fuse-config';

import { AppComponent } from 'app/app.component';
import { LayoutModule } from 'app/layout/layout.module';
import { SampleModule } from 'app/main/sample/sample.module';
import { LoginModule } from 'app/main/login/login.module';
import { MasterModule } from './main/admin/master/master.module';
import { changepasswordComponent } from './main/common/changepassword/changepassword';
import { FileuploaderComponent } from './main/common/fileuploader/fileuploader.component';
import { ViewqrcodeComponent } from './main/common/viewqrcode/viewqrcode.component';
import { MatDialogModule } from '@angular/material/dialog';
import { MsalModule } from '@azure/msal-angular';
import { AuthServiceConfig, GoogleLoginProvider, FacebookLoginProvider } from 'angularx-social-login';
import { MatGridListModule } from '@angular/material';
import { NgxQRCodeModule } from '@techiediaries/ngx-qrcode';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
const config = new AuthServiceConfig([
    {
        id: GoogleLoginProvider.PROVIDER_ID,
        provider: new GoogleLoginProvider('973360247760-4g720339hlbelthul0m7emb240bb4n4h.apps.googleusercontent.com')
    },
    // {
    //     id: FacebookLoginProvider.PROVIDER_ID,
    //     provider: new FacebookLoginProvider('Facebook-App-Id')
    // }
]);

export function provideConfig() {
    return config;
}


const appRoutes: Routes = [
    {
        path: '**',
        redirectTo: 'login'
    },
    {
        path: '',
        pathMatch: 'full',
        redirectTo: 'login'
    }
];

@NgModule({
    declarations: [
        AppComponent,
        FileuploaderComponent,
        changepasswordComponent,
        ViewqrcodeComponent
    ],
    imports: [
        BrowserModule,
        FormsModule,
        ReactiveFormsModule,
        BrowserAnimationsModule,
        HttpClientModule,
        MatGridListModule,
        RouterModule.forRoot(appRoutes, { useHash: true }),

        TranslateModule.forRoot(),

        // Material moment date module
        MatMomentDateModule,

        // Material
        MatButtonModule,
        MatIconModule,
        MatDialogModule,

        // Fuse modules
        FuseModule.forRoot(fuseConfig),
        FuseProgressBarModule,
        FuseSharedModule,
        FuseSidebarModule,
        FuseThemeOptionsModule,

        // font awesome
        AngularFontAwesomeModule,
        FontAwesomeModule,

        // App modules
        LayoutModule,
        SampleModule,
        LoginModule,
        MasterModule,
        NgxQRCodeModule,
        NgxMatSelectSearchModule,
        MsalModule.forRoot({
            clientID: '1640b9ac-99c9-49c4-93bf-8da2e59dc67a',
            redirectUri: 'https://uetracksg.com/uetracksso/',
            authority: 'https://login.microsoftonline.com/3c72c2ce-b4b6-482d-ae43-47a5c757721e',
        })
    ],
    providers: [
        {
            provide: AuthServiceConfig,
            useFactory: provideConfig
        }
    ],
    exports: [
        FileuploaderComponent,changepasswordComponent,ViewqrcodeComponent
    ],
    bootstrap: [
        AppComponent
    ],
    entryComponents: [FileuploaderComponent,changepasswordComponent,ViewqrcodeComponent]
})
export class AppModule {
}

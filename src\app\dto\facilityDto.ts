export interface SaveFacilityRequestDto {
    facilityId: number;
    facilityCode: string;
    facilityName: string;
    isActive: boolean;
    isHrmsIntegrated: boolean;
    logo: string;
}

export class FacilityGridData {
    id: number;
    active: boolean;
    code: string;
    hrms: string;
    logo: string;
    name: string;
    companyId: number;
}

export class AdminList {
    adminId: number;
    adminName: string;
}


export class FacilityGridDataResponseDto {
    facilityId: number;
    facilityName: string;
    facilityLogo: string;
    //facilityAdminList: string;    
    company: string;
    active: boolean;
    facilityCode: string;
    facilityUniqueId: string;
    FacilityModules: mstFacilityModule[];
}

export class ManageFacilityGridData {
    facilityId: number;
    facilityName: string;
    facilityLogo: string;
    //facilityAdminList: string;    
    company: string;
    active: boolean;
}

export class SaveManageFacilityRequestDto {
    facilityId: number;
    facilityName: string;
    facilityLogo?: string;    
    //facilityAdminList: string;
    company: string;    
    isActive: boolean;
    companyId: number;
    FacilityModules: mstFacilityModule[];
    createdby:number;
}


export class SaveAdminFacilityRequestDto {
    facilityId: number;
    facilityName: string;
    email: string;
    admin: boolean;
    facilityIdVal: string; 
    createdby:number;
}


export class AdminFacilityGridDataResponseDto {
    facilityId: number;
    facilityName: string;
    email: string;
    admin: boolean;
}

export class mstFacilityModule {
    refId: number;
    facilityId: number;
    moduleId: number;
    appId: number;
}
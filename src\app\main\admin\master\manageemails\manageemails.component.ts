import { Component, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { GridSearchRequestDto } from 'app/dto/common/gridDto';
import { EmailResponseDto, SaveEmailRequestDto } from 'app/dto/emailDto';
import { BaseComponent } from 'app/main/common/base.component';
import { EmailApiService } from 'app/services/api/email.api.service';
import { environment } from 'environments/environment';

@Component({
  selector: 'app-emails',
  templateUrl: './manageemails.component.html',
  styleUrls: ['./manageemails.component.scss']
})

export class ManageEmailComponent extends BaseComponent {
  @ViewChild(MatPaginator, { static: false }) paginator: MatPaginator;
  @ViewChild('formUser', { static: true }) ngForm;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  search_user = '';
  hpbsUrl = environment.hpbsUrl;
  tkn = localStorage.getItem('SSOTOKEN');
  fgEmail = new FormGroup({
    email: new FormControl('', [Validators.required, Validators.pattern('^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+.[a-zA-Z0-9-.]+$')]),
    emailId: new FormControl(''),
    active: new FormControl(true)
  });
  selectedRowIndex = 0;
  isExistingUpdateMode = false;
  displayedColumns: string[] = ['email', 'isActive', 'edit'];
  dataSource: MatTableDataSource<EmailResponseDto>;
  emailGridData: EmailResponseDto[] = [];
  length = 50;
  pageSize = 5;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageEvent: PageEvent;

  constructor(private _emailApi: EmailApiService,
    _snackBar: MatSnackBar, public _dialog: MatDialog, private _router: Router) {
    super(_snackBar);
  }

  ngOnInit() {
    const isAd = localStorage.getItem('SSOADMIN');
    if (isAd == '1' && this.tkn.trim().length > 0) {
    } else {
      localStorage.setItem('SSOUSERNAME', '');
      localStorage.setItem('SSOUSERPIC', '');
      this._router.navigateByUrl('/login');
    }

    // this.paginator.pageSize = 5;
    // this.paginator.pageIndex = 1;
    this.getAllEmails();
  }

  saveUser(): void {
    const emailObj: SaveEmailRequestDto = {
      isActive: (this.fgEmail.controls.active.value == '' || this.fgEmail.controls.active.value == null) ? false : this.fgEmail.controls.active.value,
      email: this.fgEmail.controls.email.value,
      emailId: (this.fgEmail.controls.emailId.value == '' || this.fgEmail.controls.emailId.value == null) ? 0 : this.fgEmail.controls.emailId.value,
      isDeleted: false,
      createdby:Number(localStorage.getItem('SSOUSERID'))
    };

    const obj = this.wrapRequestObject(emailObj);
    this._emailApi.saveEmails(obj)
      .subscribe(
        (data) => {
          if (data.code === 1) {
            this.showSuccessMsg('Changes has been saved successfully');
            this.ngForm.resetForm();
            this.selectedRowIndex = 0;
            this.getAllEmails();
          } else {
            this.showErrorMsg(data.msg);
          }
        },
        _error => {
        }
      );
  }

  getAllEmails(): void {
    const gridDataReq: GridSearchRequestDto = {
      page: 0,
      pageSize: this.pageSize,
      searchString: this.search_user,
      UserEmail:""
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._emailApi.getEmails(obj)
      .subscribe(
        (data) => {
          this.emailGridData = [];
          if (data.obj !== 'null') {
            const objArray = JSON.parse(data.obj.toString());
            this.length = objArray.totalRecords;
            for (const _obj of objArray['gridData']) {
              const _u: EmailResponseDto = {
                emailId: _obj.emailId,
                email: _obj.email,
                isActive: _obj.isActive,
                isDeleted: _obj.isDeleted
              };
              this.emailGridData.push(_u);
            }
          }
          this.dataSource = new MatTableDataSource(this.emailGridData);
          this.dataSource.sort = this.sort;
          this.paginator.firstPage();
        },
        _error => {
        }
      );
  }

  pagerEvent(event?: PageEvent): void {
    const gridDataReq: GridSearchRequestDto = {
      page: event.pageIndex,
      pageSize: event.pageSize,
      searchString: this.search_user,
      UserEmail:""
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._emailApi.getEmails(obj)
      .subscribe(
        (data) => {
          this.emailGridData = [];
          if (data.obj !== 'null') {
            const objArray = JSON.parse(data.obj.toString());
            for (const _obj of objArray['gridData']) {
              const _u: EmailResponseDto = {
                emailId: _obj.emailId,
                email: _obj.email,
                isActive: _obj.isActive,
                isDeleted: _obj.isDeleted
              };
              this.emailGridData.push(_u);
            }
          }
          this.dataSource = new MatTableDataSource(this.emailGridData);
          this.dataSource.sort = this.sort;
        },
        _error => {
        }
      );
  }

  resetExtraDetails(): void {
    this.search_user = '';
    this.selectedRowIndex = 0;
    this.getAllEmails();
    this.isExistingUpdateMode = false;
  }

  loadEmailDetails(row): void {

    this.isExistingUpdateMode = true;
    this.selectedRowIndex = row.emailId;
    this.fgEmail.controls.emailId.setValue(row.emailId);
    this.fgEmail.controls.email.setValue(row.email);
    this.fgEmail.controls.active.setValue(row.isActive);

  }

  deleteEmailDetails(row): void {
    this.selectedRowIndex = row.emailId;
    const emailObj: SaveEmailRequestDto = {
      isActive: row.isActive,
      email: row.email,
      emailId: row.emailId,
      isDeleted: true,
      createdby:Number(localStorage.getItem('SSOUSERID'))
    };


    const obj = this.wrapRequestObject(emailObj);
    this._emailApi.saveEmails(obj)
      .subscribe(
        (data) => {
          if (data.code === 1) {
            this.showSuccessMsg('Changes has been deleted successfully');
            this.ngForm.resetForm();
            this.selectedRowIndex = 0;
            this.getAllEmails();
          } else {
            this.showErrorMsg(data.msg);
          }
        },
        _error => {
        }
      );
  }

  navigateToHousekeeping(): void {
    window.location.href = this.hpbsUrl + '#/login?token=' + this.tkn;
  }

}

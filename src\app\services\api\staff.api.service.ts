import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ApiRequest, ApiResponse } from 'app/dto/common/commonDto';
import { Observable } from 'rxjs';
import { BaseApiServiceService } from './baseapi.service';

@Injectable({
  providedIn: 'root'
})
export class StaffApiService extends BaseApiServiceService {

  constructor(public http: HttpClient, public router: Router) {
    super();
  }

  getAllStaffs(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}staff/list`,
        {
          'data': request
        });
  }

  getStaff(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}staff/get`,
        {
          'data': request
        });
  }

  saveStaff(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}staff/save`,
        {
          'data': request
        });
  }

  deleteStaff(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}staff/delete`,
        {
          'data': request
        });
  }

  getLastEmployeeId(): Observable<ApiResponse> {
    return this
      .http
      .get<ApiResponse>(`${this.baseUrl}staff/getlastemployeeid`);
  }
} 
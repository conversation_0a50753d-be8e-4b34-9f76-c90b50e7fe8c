import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ApiRequest, ApiResponse } from 'app/dto/common/commonDto';
import { Observable } from 'rxjs';
import { BaseApiServiceService } from './baseapi.service';


@Injectable({
  providedIn: 'root'
})

export class UserApiService extends BaseApiServiceService {

  constructor(public http: HttpClient, public router: Router) {
    super();
  }

  getAllUsers(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}user/loadallusers`,
        {
          'data': request
        });
  }
  getAllUserRoles(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}user/saveuser`,
        {
          'data': request
        });

  }

  getAllHpbsUserRoles(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}user/loadalluserroles`,
        {
          'data': request
        });

  }

  getAllFacilities(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}facility/loadallfacilities`,
        {
          'data': request
        });

  }

  saveUser(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}user/saveuser`,
        {
          'data': request
        });
  }

  loginCustom(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}user/login`,
        {
          'data': request
        });
  }
  loginExternal(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}externallogin/login`,
        {
          'data': request
        });
  }
  getFacilityAdmins(): Observable<ApiResponse> {
    return this
      .http
      .get<ApiResponse>(`${this.baseUrl}facility/admins`);
  }

}

export class SaveUserRequestDto {
    userId: number;
    userName: string;
    fullName: string;
    password: string;
    email: string;
    isActive: boolean;
    isAdmin: boolean;
createdby:number;
    userApps: mstUserApp[];
    userInsts: mstUserInstance[];
    userModules: mstUserModule[]
}



export class mstUserApp {
    refId: number;
    userId: number;
    appId: number;
    roleId: number;
}


export class mstUserInstance {
    refId: number;
    userId: number;
    facilityId: number;
    appId: number;
}

export class mstUserModule {
    refId: number;
    userId: number;
    moduleId: number;
    appId: number;
}

import {
    <PERSON>ttp<PERSON><PERSON>,
    <PERSON>ttp<PERSON>nter<PERSON>,
    HttpHandler,
    HttpRequest,
    HttpResponse,
    HttpErrorResponse
   } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { retry, catchError } from 'rxjs/operators';
// import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
// import { CommonApiServiceService } from 'app/services/common.api.service';
import { Injectable } from '@angular/core';
@Injectable()
   export class HttpErrorInterceptor implements HttpInterceptor {
      
       constructor(private _router: Router) {
       }
    intercept(request: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
      return next.handle(request)
        .pipe(
          retry(1),
          catchError((error: HttpErrorResponse) => {
            let errorMessage = '';
            if (error.error instanceof ErrorEvent) {
              // client-side error
              errorMessage = `Error: ${error.error.message}`;
            } else {
              // server-side error
              //errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
              alert(error.status)
              if(error.status===403){
                    //session timeout
                   // localStorage.setItem('hpbsToken', '');
                  //  localStorage.setItem('currentFacility', '0');
                  //  this._toastr.error(this.getLabel('SESSION_TIMEOUT_MSG'));
                 //   this._router.navigate(['/login', { returnurl: this._router.url}]);
              }
              if(error.status===401){
                //session timeout
              ///  localStorage.setItem('hpbsToken', '');
              //  localStorage.setItem('currentFacility', '0');
              //  this._toastr.error(this.getLabel('SESSION_TIMEOUT_MSG_NO_ACCESS'));
               // this._router.navigate(['/login']);
          }
            }
           // window.alert(errorMessage);
            return throwError(errorMessage);
          })
        )
    }
    getLabel(_lblName) {
        //return this._commonService.getLabelName(_lblName);
      }
   }
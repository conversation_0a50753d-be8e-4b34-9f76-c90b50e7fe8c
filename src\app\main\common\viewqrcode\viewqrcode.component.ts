import { Component, ElementRef, OnInit, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';


@Component({
  selector: 'app-viewqrcode',
  templateUrl: './viewqrcode.component.html',
  styleUrls: ['./viewqrcode.component.scss']
})
export class ViewqrcodeComponent implements OnInit {
    value = '';
  constructor(
    public dialogRef: MatDialogRef<ViewqrcodeComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData) {
        this.value= data.value;
    }
    ngOnInit(): void {
    }
    
}

export interface DialogData {
    value: string;
  name: string;
}
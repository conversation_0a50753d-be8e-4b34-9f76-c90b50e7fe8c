import { Moment } from "moment";

export class SaveCompanyRequestDto {
    companyId: number;
    companyName: string;
    effectiveDate: Moment;
    contractExpiryDate: Moment;
    country: string;
    isActive: boolean;
    moduleInst: mstCompanyModule[];
    companyLogo?: string;    
    countryCode?: string;    
    smsUrl?: string;  
    createdby:number;
}


export class mstCompanyModule {
    companyId: number;
    moduleId: number;
}

.mat-card {
    margin-bottom: 24px;
}

.mat-form-field {
    width: 100%;
}

.mat-radio-group {
    margin: 16px 0;
}

.highlight {
    background-color: #f5f5f5;
}

.mat-sm {
    min-width: 32px;
    padding: 0 8px;
    margin: 0 4px;
}

.mat-top-margin {
    margin-top: 16px;
}

.mr-20 {
    margin-right: 20px;
}

.mb-24 {
    margin-bottom: 24px;
}

.mt-20 {
    margin-top: 20px;
}

.pr-4 {
    padding-right: 4px;
}

table {
    width: 100%;
}

.mat-column-actions {
    width: 120px;
    text-align: center;
}

.mat-column-emailAddress {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.photo-preview-container {
    margin-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px;
    background-color: #f5f5f5;
    border-radius: 4px;
}

.photo-preview {
    max-width: 70px;
    max-height: 70px;
    object-fit: contain;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ml-8 {
    margin-left: 8px;
} 
.master-button
{
    color: #000000 !important;
    background-color: #ffffff !important;
    text-align: center !important;
}
.master-button mat-icon{
    color: #797575 !important
}
.noRecordsMessage {
    text-align: center;
    padding: 20px;
    color: #666;
    font-size: 16px;
}

.noRecordsMessage mat-icon {
    vertical-align: middle;
    margin-right: 8px;
}
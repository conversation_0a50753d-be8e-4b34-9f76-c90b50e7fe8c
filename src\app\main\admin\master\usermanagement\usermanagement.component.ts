import { Component, ViewChild } from '@angular/core';
import { BaseComponent } from 'app/main/common/base.component';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { ReplaySubject, Subject } from 'rxjs';
import { MatSort } from '@angular/material/sort';
import { UserApiService } from 'app/services/api/user.api.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { GridSearchRequestDto } from 'app/dto/common/gridDto';
import { FacilityApiService } from 'app/services/api/facility.api.service.service';
import { UserGridData, RoleResponseDto, SaveRoleRequestDto, UserRoleRequestDto, UsersResponseDto } from 'app/dto/userDto';
import { SaveUserRequestDto, mstUserApp, mstUserInstance, mstUserModule } from 'app/dto/saveUserDto';
import { Router } from '@angular/router';
import { environment } from 'environments/environment';
import { FacilityGridData } from 'app/dto/facilityDto';
import { ModuleApiService } from 'app/services/api/modules.api.service';
import { ModuleList } from 'app/dto/countryDto';
import { userIDDto } from 'app/dto/userDto';
import { CompanyApiService } from 'app/services/api/company.api.service';
import { MatOptionSelectionChange, MatSelectChange } from '@angular/material';
import { CompanyList } from 'app/dto/companyDto';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-usermanagement',
  templateUrl: './usermanagement.component.html',
  styleUrls: ['./usermanagement.component.scss']
})

export class UsermanagementComponent extends BaseComponent {
  @ViewChild('formUser', { static: true }) ngForm;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  usersFilterCtrl: FormControl = new FormControl();
  filteredUsersList: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  search_user = '';
  search_userId = '';
  search_userEmail = '';
  hpbsUrl = environment.hpbsUrl;
  tkn = localStorage.getItem('SSOTOKEN');
  fgUser = new FormGroup({
    //  method: new FormControl(''),
    password: new FormControl(''),
    name: new FormControl(''),
    email: new FormControl('', [Validators.required, Validators.pattern('^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+.[a-zA-Z0-9-.]+$')]),
    id: new FormControl(''),
    active: new FormControl(true),
    //facilities_hpbs: new FormControl([], [Validators.required]),
    facilities_hpbs: new FormControl(''),
    moduleDesc: new FormControl(''),
    admin: new FormControl(false),
    companyControl: new FormControl(''),
  });
  selectedRowIndex = 0;
  moduleList: ModuleList[];
  checkedOptions: ModuleList[];
  isExistingUpdateMode = false;
  displayedColumns: string[] = ['userName', 'userFullName', 'isActive'];
  dataSource: MatTableDataSource<UsersResponseDto>;
  userGridData: UsersResponseDto[] = [];
  userList: UsersResponseDto[] = [];
  facilityList: FacilityGridData[] = [];
  facilityListVal: FacilityGridData[] = [];
  hpbsUserRoleList: RoleResponseDto[] = [];
  length = 50;
  pageSize = 5;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageEvent: PageEvent;
  show: boolean = true;
  shouldBeReadonly = '';
  companyList: CompanyList[];
  company = 0;
  SSOUSERCOMPANYID = '0';
  constructor(private _userApi: UserApiService, private _facilityApi: FacilityApiService, private _moduleApi: ModuleApiService, private _companyApi: CompanyApiService,
    _snackBar: MatSnackBar, public _dialog: MatDialog, private _router: Router) {
    super(_snackBar);
  }

  ngOnInit() {
    const isAd = localStorage.getItem('SSOADMIN');
    if (isAd == '1' && this.tkn.trim().length > 0) {
    } else {
      localStorage.setItem('SSOUSERNAME', '');
      localStorage.setItem('SSOUSERPIC', '');
      this._router.navigateByUrl('/login');
    }

    //this.getAllFacilities();
    //this.getHpbsUserRoles();
    this.getAllModules();
    this.getAllUsers();
    this.getAllUsersList();
    this.getCompaniesList();
    const isROLEID = localStorage.getItem('SSOROLEID');
    if (isROLEID != '0') {
      this.show = false;
      this.SSOUSERCOMPANYID = localStorage.getItem('SSOUSERCOMPANYID');
      this.getAllFacilities(this.SSOUSERCOMPANYID);
    }
    {
      this.show = this.show;
    }
    this.shouldBeReadonly = '';

    this.usersFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.filterFacilityList();
      });
  }
  getCompaniesList(): void {
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: GridSearchRequestDto = {
      page: 0,
      pageSize: this.pageSize,
      searchString: this.search_user,
      UserEmail: this.search_userEmail
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._companyApi.getCompanies(obj)
      .subscribe(
        (data: any) => {
          this.companyList = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray) {
            const _c: CompanyList = {
              companyId: _obj.companyId,
              companyName: _obj.companyName
            };
            this.companyList.push(_c);
          }
        },
        _error => {
        }
      );
  }
  getfacilityList(event: MatOptionSelectionChange, state: any) {
    this.getAllFacilities(event.source.value);
  }
  getAllFacilities(companyId): void {
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: GridSearchRequestDto = {
      page: this.paginator.pageIndex,
      pageSize: 500,
      searchString: this.search_userEmail,
      UserEmail: this.search_userEmail
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._facilityApi.getAllFacilities(obj)
      .subscribe(
        (data) => {
          this.facilityList = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray['gridData']['facilitiesResponse']) {
            const _f: FacilityGridData = {
              id: parseInt(_obj.facilityId, 10),
              active: _obj.isActive,
              code: _obj.facilityCode,
              hrms: _obj.isHrmsIntegrated,
              logo: _obj.logo,
              name: _obj.facilityName,
              companyId: _obj.companyId
            };

            this.facilityList.push(_f);
            if (companyId > 0) {
              this.facilityList = this.facilityList.filter(com => com.companyId == companyId);
            }
          }

        },
        _error => {
        }
      );
  }

  getAllModules(): void {
    this.search_userId = localStorage.getItem('SSOUSERCOMPANYID');
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: userIDDto = {
      id: this.search_userId,
      UserEmail: this.search_userEmail
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._moduleApi.getModules(obj)
      .subscribe(
        (data: any) => {
          this.moduleList = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray) {
            const _c: ModuleList = {
              moduleId: _obj.moduleId,
              moduleDesc: _obj.moduleDesc,
              URL: _obj.URL,
              Description: ''
            };
            this.moduleList.push(_c);
          }
        },
        _error => {
        }
      );
  }

  saveUser(): void {
    //console.log(this.fgUser);
    const userObj: SaveUserRequestDto = {
      isActive: (this.fgUser.controls.active.value == '' || this.fgUser.controls.active.value == null) ? false : this.fgUser.controls.active.value,
      fullName: this.fgUser.controls.name.value,
      password: this.fgUser.controls.password.value,
      email: this.fgUser.controls.email.value,
      userName: this.fgUser.controls.email.value,
      userId: (this.fgUser.controls.id.value == '' || this.fgUser.controls.id.value == null) ? 0 : this.fgUser.controls.id.value,
      userApps: [],
      userInsts: [],
      userModules: [],
      isAdmin: (this.fgUser.controls.admin.value == '' || this.fgUser.controls.admin.value == null) ? false : this.fgUser.controls.admin.value,
      //isAdmin: false,
      createdby: Number(localStorage.getItem('SSOUSERID'))
    };

    // facility mapp
    this.fgUser.controls.facilities_hpbs.value.forEach(fac => {
      const usrInst: mstUserInstance = {
        refId: 0,
        appId: 1,
        facilityId: fac,
        userId: 0
      };
      userObj.userInsts.push(usrInst);
    });

    // module mapp
    this.fgUser.controls.moduleDesc.value.forEach(module => {
      const usrModules: mstUserModule = {
        refId: 0,
        appId: 1,
        moduleId: module,
        userId: 0
      };
      userObj.userModules.push(usrModules);
    });

    const obj = this.wrapRequestObject(userObj);
    this._userApi.saveUser(obj)
      .subscribe(
        (data) => {
          if (data.code === 1) {
            this.showSuccessMsg('Changes has been saved successfully');
            this.ngForm.resetForm();
            this.selectedRowIndex = 0;
            this.pageSize = 5;
            this.getAllUsers();
          } else if (data.code === -1) {
            this.showErrorMsg('User email already exists');
          }
          else {
            this.showErrorMsg(data.msg);
          }
        },
        _error => {
        }
      );

  }

  getAllUsers(): void {
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: GridSearchRequestDto = {
      page: 0,
      pageSize: this.pageSize,
      searchString: this.search_user,
      UserEmail: this.search_userEmail
    };
    this.length = 0;
    const obj = this.wrapRequestObject(gridDataReq);
    this._userApi.getAllUsers(obj)
      .subscribe(
        (data) => {
          this.userGridData = [];
          // this.userList = [];
          const objArray = JSON.parse(data.obj.toString());
          this.length = objArray ? objArray.totalRecords : 0;
          if (objArray && objArray['gridData']) {
            for (const _obj of objArray['gridData']) {
              const _u: UsersResponseDto = {
                email: _obj.userName,
                isActive: _obj.isActive,
                userFullName: _obj.userFullName,
                userId: _obj.userId,
                userInsts: _obj.UserInsts,
                userApps: _obj.UserApps,
                userModules: _obj.UserModules,
                userName: _obj.userName,
                isExternalLogin: false,
                isAdmin: _obj.isAdmin,
                Edit: _obj.Edit,
              };
              this.userGridData.push(_u);
              // this.userList.push(_u);
              // this.filteredUsersList.next(this.userList.slice());

            }
          }
          this.dataSource = new MatTableDataSource(this.userGridData);
          this.dataSource.sort = this.sort;
          this.paginator.firstPage();
          this.shouldBeReadonly = '';
        },
        _error => {
        }
      );
  }
    getAllUsersList(): void {
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: GridSearchRequestDto = {
      page: 0,
      pageSize: 0,
      searchString: this.search_user,
      UserEmail: this.search_userEmail
    };
    this.length = 0;
    const obj = this.wrapRequestObject(gridDataReq);
    this._userApi.getAllUsers(obj)
      .subscribe(
        (data) => {
          this.userList = [];
          const objArray = JSON.parse(data.obj.toString());
          this.length = objArray ? objArray.totalRecords : 0;
          if (objArray && objArray['gridData']) {
            for (const _obj of objArray['gridData']) {
              const _u: UsersResponseDto = {
                email: _obj.userName,
                isActive: _obj.isActive,
                userFullName: _obj.userFullName,
                userId: _obj.userId,
                userInsts: _obj.UserInsts,
                userApps: _obj.UserApps,
                userModules: _obj.UserModules,
                userName: _obj.userName,
                isExternalLogin: false,
                isAdmin: _obj.isAdmin,
                Edit: _obj.Edit,
              };
              this.userList.push(_u);
              this.filteredUsersList.next(this.userList.slice());

            }
          }
        },
        _error => {
        }
      );
  }

  pagerEvent(event?: PageEvent): void {
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: GridSearchRequestDto = {
      page: event.pageIndex,
      pageSize: event.pageSize,
      searchString: this.search_user,
      UserEmail: this.search_userEmail
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._userApi.getAllUsers(obj)
      .subscribe(
        (data) => {
          this.userGridData = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray['gridData']) {
            const _u: UsersResponseDto = {
              email: _obj.userName,
              isActive: _obj.isActive,
              userFullName: _obj.userFullName,
              userId: _obj.userId,
              userInsts: _obj.UserInsts,
              userApps: _obj.UserApps,
              userName: _obj.userName,
              userModules: _obj.UserModules,
              isExternalLogin: false,
              isAdmin: _obj.isAdmin,
              Edit: _obj.Edit,
            };
            this.userGridData.push(_u);
          }
          this.dataSource = new MatTableDataSource(this.userGridData);
          this.dataSource.sort = this.sort;
        },
        _error => {
        }
      );
  }

  resetExtraDetails(): void {
    this.search_user = '';
    this.selectedRowIndex = 0;
    this.getAllUsers();
    this.pageSize = 5;
    this.isExistingUpdateMode = false;
    this.shouldBeReadonly = '';
  }

  private filterFacilityList() {
    if (!this.userList) return;
    let search = this.usersFilterCtrl.value;
    if (!search) {
      this.filteredUsersList.next(this.userList.slice());
      return;
    }
    search = search.toLowerCase();
    this.filteredUsersList.next(
      this.userList.filter(a =>
        a.userFullName.toLowerCase().includes(search)
      )
    );
  }

  onUserSelect(event: MatSelectChange): void {
    const selectedUserId = event.value;
    const selectedUser = this.userList.find(user => user.userId === selectedUserId);

    if (selectedUser) {
      this.loadUserDetails(selectedUser);
    }
  }

  loadUserDetails(row): void {
    if (row.Edit == 0) {
      this.shouldBeReadonly = 'true';
      this.isExistingUpdateMode = true;
      this.selectedRowIndex = row.userId;
      let _hpbs_facilities = [];
      let userModulesList = [];
      this.company = 0;

      row.userInsts.forEach(_userApps => {
        if (_userApps.AppId === 1) {
          _hpbs_facilities.push(_userApps.FacilityId);
        }
      });


      row.userModules.forEach(_userModules => {
        if (_userModules.AppId === 1) {
          userModulesList.push(_userModules.ModuleId);
        }
      });
      this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
      const gridDataReq: GridSearchRequestDto = {
        page: this.paginator.pageIndex,
        pageSize: 500,
        searchString: this.search_userEmail,
        UserEmail: this.search_userEmail
      };
      const obj = this.wrapRequestObject(gridDataReq);
      this._facilityApi.getAllFacilities(obj)
        .subscribe(
          (data) => {
            this.facilityList = [];
            this.facilityListVal = [];
            const objArray = JSON.parse(data.obj.toString());
            for (const _obj of objArray['gridData']['facilitiesResponse']) {
              const _f: FacilityGridData = {
                id: parseInt(_obj.facilityId, 10),
                active: _obj.isActive,
                code: _obj.facilityCode,
                hrms: _obj.isHrmsIntegrated,
                logo: _obj.logo,
                name: _obj.facilityName,
                companyId: _obj.companyId
              };

              this.facilityList.push(_f);


            }
            this.facilityListVal = this.facilityList.filter(com => com.id == _hpbs_facilities[0]);
            for (const _obj of this.facilityListVal) {
              this.company = _obj.companyId;
              break;
            }
            if (this.company > 0) {
              this.facilityList = this.facilityList.filter(com => com.companyId == this.company);
            }
            this.fgUser.setValue({
              id: row.userId,
              name: row.userFullName,
              active: row.isActive,
              password: '',
              facilities_hpbs: _hpbs_facilities,
              moduleDesc: userModulesList,
              email: row.userName,
              admin: row.isAdmin,
              companyControl: this.company
            });

          },
          _error => {
          }
        );

    }
    else {
      this.showErrorMsg('You Cannot Edit User Details. User have multiple facilities');
      this.ngForm.resetForm();
    }
  }

  navigateToHousekeeping(): void {
    window.location.href = this.hpbsUrl + '#/login?token=' + this.tkn;
  }
}

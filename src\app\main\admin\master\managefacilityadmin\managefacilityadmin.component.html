<div id="forms" class="page-layout simple fullwidth" fxLayout="column">
    <div class="header accent p-24 h-20" fxLayout="row" fxLayoutAlign="start center">
        <div fxLayout="column" fxLayoutAlign="center start">
            <div fxLayout="row" fxLayoutAlign="start center">
                <h2>Manage Facility Admin</h2>
            </div>
        </div>
    </div>
    <div class="content p-24">
        <div class="mb-24" fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="space-around start" fxLayout.gt-md="row">
            <div fxFlex="100" fxLayout="column">
                <div fxFlex="100" fxLayout="column" class="mt-20">
                    <div fxLayout="row wrap" fxLayoutGap="10px" fxLayoutAlign="start" fxLayout.gt-md="row wrap">
                        <div fxFlex="40" fxLayout="column" style="align-items: center">
                            <form class="mat-card mat-elevation-z4 p-24" fxLayout="column" fxLayoutAlign="start"
                                [formGroup]="fgFacilityAdmin" fxFlex="1 0 shrink" name="form"
                                (ngSubmit)="saveFacilities()" #formUser="ngForm">
                                <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                                    <mat-form-field appearance="outline" fxFlex="90" class="pr-4">
                                        <mat-label>Email</mat-label>
                                        <input matInput required type="email" formControlName="email">
                                        <mat-icon matSuffix class="secondary-text">email</mat-icon>
                                        <mat-error>Email is required!</mat-error>
                                    </mat-form-field>
                                </div>
                                <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink" *ngIf=show>
                                    <mat-form-field appearance="outline" fxFlex="90" class="pr-4">
                                        <mat-label>Company</mat-label>
                                        <mat-select required formControlName="companyControl"
                                            (selectionChange)="getfacilityList($event)">
                                            <mat-option [value]="company.companyId" *ngFor="let company of companyList">
                                                {{company.companyName}}
                                            </mat-option>
                                        </mat-select>
                                        <mat-error>Company is required!</mat-error>
                                    </mat-form-field>
                                </div>
                                <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                                    <mat-form-field appearance="outline" fxFlex="90" class="pr-4">
                                        <mat-label>Facility(ies)</mat-label>
                                        <mat-select required formControlName="facilities" [(ngModel)]="checkedOptions"
                                            multiple>
                                            <input type="text" #text matInput placeholder="search facility(ies)..."
                                                style="height: 40px;padding: 25px;">
                                            <mat-option [value]="fac" *ngFor="let fac of facilityList"
                                                [ngStyle]="{ display : (text.value !== '' && fac.code.toLowerCase().indexOf(text.value.toLowerCase()) === -1) ? 'none' : 'flex' }">
                                                <strong>{{fac.code}} &nbsp;</strong> - {{fac.name}}
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                </div>
                                <div fxFlex="60" fxLayout="column">
                                    <div fxLayout="row wrap" fxLayoutAlign="start center" fxFlex="1 0 auto">
                                        <div fxLayout="row wrap" fxFlex="100" fxLayoutAlign="start center">
                                            <mat-checkbox fxFlex="50" formControlName="admin">Is Company
                                                Admin</mat-checkbox>
                                        </div>
                                    </div>
                                    <div fxLayout="row" fxLayoutAlign="end center" fxFlex="1 0 auto">
                                        <button mat-stroked-button type="reset" (click)="resetExtraDetails()"
                                            class="mat-top-margin mr-20" color="primary">Reset</button>
                                        <button mat-flat-button type="submit" [disabled]="fgFacilityAdmin.invalid"
                                            class="mat-top-margin mr-20" color="primary">Save
                                            Changes</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div fxFlex="55" fxLayout="column">
                            <div class="mat-card mat-elevation-z4 p-24">
                                <mat-form-field style="width:50%;">
                                    <button mat-button matPrefix mat-icon-button>
                                        <mat-icon>search</mat-icon>
                                    </button>
                                    <input matInput type="text" (keyup)="getFacilityAdminData()"
                                        [(ngModel)]="search_user" placeholder="Filter Email">
                                    <button *ngIf="search_user" mat-button matSuffix mat-icon-button aria-label="Clear"
                                        (click)="search_user=''; getFacilityAdminData();">
                                        <mat-icon>close</mat-icon>
                                    </button>
                                </mat-form-field>
                                <table mat-table [dataSource]="dataSource" matSort>
                                    <ng-container matColumnDef="email">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
                                        <td mat-cell *matCellDef="let row"> {{row.email}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="facilityName">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Facility </th>
                                        <td mat-cell *matCellDef="let row"> {{row.facilityName }}</td>
                                    </ng-container>
                                    <ng-container matColumnDef="admin">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Admin </th>
                                        <td mat-cell *matCellDef="let row"> {{row.admin===true?'Yes': 'No'}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="edit">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Edit </th>
                                        <td mat-cell *matCellDef="let row" [style.color]="">
                                            <button mat-stroked-button color="primary"
                                                (click)="loadAdminFacilityDetails(row)" matSuffix class="mat-sm">
                                                <mat-icon>edit</mat-icon>
                                            </button>
                                        </td>
                                    </ng-container>
                                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                                        [ngClass]="{'highlight': selectedRowIndex == row.facilityId}">
                                    </tr>
                                </table>
                                <div *ngIf="!dataSource?.data?.length && search_user" class="noRecordsMessage">
                                    <mat-icon>search_off</mat-icon>
                                    No matching records found. Please try a different search.
                                </div>
                                <mat-paginator [length]="length" [pageSize]="pageSize"
                                    [pageSizeOptions]="pageSizeOptions" (page)="pagerEvent($event)"></mat-paginator>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mb-24" fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="space-around start" fxLayout.gt-md="row">
        </div>
    </div>
</div>
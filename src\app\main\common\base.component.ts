import { OnInit } from '@angular/core';
import { ApiRequest } from 'app/dto/common/commonDto';
import { request } from 'http';
import {
    MatSnackBar,
    MatSnackBarHorizontalPosition,
    MatSnackBarVerticalPosition,
    MatSnackBarConfig,
  } from '@angular/material/snack-bar';

  

export class BaseComponent implements OnInit {


    horizontalPosition: MatSnackBarHorizontalPosition = 'center';
    verticalPosition: MatSnackBarVerticalPosition = 'top';


    constructor(private _snackBar: MatSnackBar){

    }

    ngOnInit(): void {
    }
    
    wrapRequestObject(obj: any): ApiRequest {
       const req: ApiRequest = {
            facilityId: 1,
            userLanguage:'en',
            requestObj: obj
        };
       return req;
    }

    showSuccessMsg(msg): void{
        
        const config = new MatSnackBarConfig();
        config.verticalPosition = this.verticalPosition;
        config.horizontalPosition = this.horizontalPosition;
        config.panelClass = ['alert-red'];
        config.duration = 3000;
    
        // config.extraClasses = this.addExtraClass ? ['test'] : undefined;
        this._snackBar.open(msg, 'Success', config);
    }

    showErrorMsg(msg): void{
        
        const config = new MatSnackBarConfig();
        config.verticalPosition = this.verticalPosition;
        config.horizontalPosition = this.horizontalPosition;
        config.panelClass = ['alert-red'];
        config.duration = 3000;
    
        // config.extraClasses = this.addExtraClass ? ['test'] : undefined;
        this._snackBar.open(msg, 'Error', config);
    }
}

<div id="forms" class="page-layout simple fullwidth" fxLayout="column">
    <div class="header accent p-24 h-20" fxLayout="row" fxLayoutAlign="start center">
        <div fxLayout="column" fxLayoutAlign="center start">
            <div fxLayout="row" fxLayoutAlign="start center">
                <h2>Manage Facility</h2>
                <!-- <button mat-raised-button style="margin-left: 50px;" color="primary" (click)="navigateToHousekeeping()">Go To Houskeeping</button>  -->
            </div>
        </div>
    </div>
    <div class="content p-24">
        <div class="mb-24" fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="space-around start" fxLayout.gt-md="row">
            <div fxFlex="100" fxLayout="column">
                <div fxFlex="100" fxLayout="column" class="mt-20">
                    <div fxLayout="row wrap" fxLayoutGap="10px" fxLayoutAlign="start" fxLayout.gt-md="row wrap">
                        <div fxFlex="40" fxLayout="column" style="align-items: center">
                            <form class="mat-card mat-elevation-z4 p-24" fxLayout="column" fxLayoutAlign="start"
                                [formGroup]="fgFacility" fxFlex="1 0 shrink" name="form" (ngSubmit)="saveFacilities()"
                                #formUser="ngForm">
                                <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                                    <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                                        <mat-label>Facility Name</mat-label>
                                        <input matInput required type="text" formControlName="facilityName">
                                        <mat-icon matSuffix class="secondary-text">account_circle</mat-icon>
                                        <mat-error>Facility Name is required!</mat-error>
                                    </mat-form-field>
                                    <mat-form-field appearance="outline" fxFlex="90" class="pr-4">
                                        <mat-label>Company</mat-label>
                                        <mat-select formControlName="companyControl">
                                            <mat-option [value]="company.companyId" *ngFor="let company of companyList">
                                                {{company.companyName}}
                                            </mat-option>
                                        </mat-select>
                                        <mat-error>Company is required!</mat-error>
                                    </mat-form-field>
                                </div>
                                <div fxFlex="60" fxLayout="column">
                                    <div fxLayout="row wrap" fxLayoutAlign="start center" fxFlex="1 0 auto">
                                        <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                                            <mat-label>Facility logo</mat-label>
                                            <input matInput readonly="readonly" formControlName='logo'>
                                            <button mat-stroked-button color="primary" (click)="uploadLogo()" matSuffix
                                                class="mat-small">Upload</button>
                                            <!-- <input id="myInput" type="file" style="visibility:hidden" /> -->
                                        </mat-form-field>
                                        <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                                            <mat-label>Enabled Modules</mat-label>
                                            <mat-select formControlName="moduleDesc" multiple required>
                                                <mat-option [value]="module.moduleId" *ngFor="let module of moduleList">
                                                    {{ module.moduleDesc }}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </div>
                                    <div fxLayout="row wrap" fxLayoutAlign="start center" fxFlex="1 0 auto">
                                        <div fxLayout="row wrap" fxFlex="100" fxLayoutAlign="start center">
                                            <mat-checkbox fxFlex="50" formControlName="active">Active</mat-checkbox>
                                        </div>
                                    </div>
                                    <div fxLayout="row" fxLayoutAlign="end center" fxFlex="1 0 auto">
                                        <button mat-stroked-button type="reset" (click)="resetExtraDetails()"
                                            class="mat-top-margin mr-20" color="primary">Reset</button>
                                        <button mat-flat-button type="submit"
                                            [disabled]="fgFacility.invalid || !isFacilityEdit"
                                            class="mat-top-margin mr-20" color="primary">Save
                                            Changes</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div fxFlex="55" fxLayout="column">
                            <div class="mat-card mat-elevation-z4 p-24">
                                <mat-form-field style="width:50%;">
                                    <button mat-button matPrefix mat-icon-button>
                                        <mat-icon>search</mat-icon>
                                    </button>
                                    <input matInput type="text" (keyup)="getFacilityData()" [(ngModel)]="search_user"
                                        placeholder="Filter Facility">
                                    <button *ngIf="search_user" mat-button matSuffix mat-icon-button aria-label="Clear"
                                        (click)="search_user=''; getFacilityData();">
                                        <mat-icon>close</mat-icon>
                                    </button>
                                </mat-form-field>
                                <table mat-table [dataSource]="dataSource" matSort>
                                    <ng-container matColumnDef="facilityName">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Facility Name </th>
                                        <td mat-cell *matCellDef="let row"> {{row.facilityName}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="facilityCode">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Facility Code </th>
                                        <td mat-cell *matCellDef="let row"> {{row.facilityCode}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="QR">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>QR </th>
                                        <td mat-cell *matCellDef="let row">
                                            <button mat-stroked-button color="primary" (click)="viewQRcode(row)"
                                                matSuffix class="mat-sm">View</button>
                                        </td>
                                    </ng-container>
                                    <ng-container matColumnDef="company">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Company </th>
                                        <td mat-cell *matCellDef="let row"> {{row.company }}</td>
                                    </ng-container>
                                    <ng-container matColumnDef="facilityLogo">
                                        <th mat-header-cell *matHeaderCellDef> Facility Logo </th>
                                        <td mat-cell *matCellDef="let row"><img height="48px" width="48px"
                                                [src]=row.facilityLogo />
                                        </td>
                                    </ng-container>
                                    <ng-container matColumnDef="facilityAdmins">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Facility Admin </th>
                                        <td mat-cell *matCellDef="let row"> {{ row.facilityAdminList | facilityAdmin:
                                            row.facilityAdminList}}</td>
                                    </ng-container>
                                    <ng-container matColumnDef="isActive">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Active </th>
                                        <td mat-cell *matCellDef="let row"> {{row.isActive===true?'Yes': 'No'}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="edit">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Edit </th>
                                        <td mat-cell *matCellDef="let row" [style.color]="">
                                            <button mat-stroked-button color="primary"
                                                (click)="loadFacilityDetails(row)" matSuffix class="mat-sm">
                                                <mat-icon>edit</mat-icon>
                                            </button>
                                        </td>
                                    </ng-container>
                                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                                        [ngClass]="{'highlight': selectedRowIndex == row.facilityId}">
                                    </tr>
                                </table>
                                <div *ngIf="!dataSource?.data?.length && search_user" class="noRecordsMessage">
                                    <mat-icon>search_off</mat-icon>
                                    No matching records found. Please try a different search.
                                </div>
                                <mat-paginator [length]="length" [pageSize]="pageSize"
                                    [pageSizeOptions]="pageSizeOptions" (page)="pagerEvent($event)"></mat-paginator>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="mb-24" fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="space-around start" fxLayout.gt-md="row">
        </div>
    </div>
</div>
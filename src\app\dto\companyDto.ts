import { mstCompanyModule } from './saveCompanyDto';
import { mstUserInstance, mstUserApp } from './saveUserDto';

export class CompanyResponseDto {
    companyId: number;
    companyName: string;
    effectiveDate: string;
    contractExpiryDate: string;    
    country: string;
    active: boolean;
    moduleInst: mstCompanyModule[];
    companyLogo: string; 
    countryCode?: string;    
    smsUrl?: string;   
}

export class companyGridData {
    companyId: number;
    companyName: string;
    effectiveDate: string;
    contractExpiryDate: string;    
    country: string;
    active: boolean;
    moduleInst: mstCompanyModule[];
    countryCode?: string;    
    smsUrl?: string;   
}

export class CompanyList {
    companyId: number;
    companyName: string;
}

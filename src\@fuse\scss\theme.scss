@import "src/@fuse/components/countdown/countdown.theme";
@import "src/@fuse/components/material-color-picker/material-color-picker.theme";
@import "src/@fuse/components/search-bar/search-bar.theme";
@import "src/@fuse/components/shortcuts/shortcuts.theme";
@import "src/@fuse/components/sidebar/sidebar.theme";
@import "src/@fuse/components/theme-options/theme-options.theme";
@import "src/@fuse/components/widget/widget.theme";

@mixin fuse-core-theme($theme) {

    // Theming for Fuse core styles
    @include colors-theme($theme);
    @include cards-theme($theme);
    @include material-theme($theme);
    @include page-layouts-theme($theme);
    @include typography-theme($theme);

    // Theming for Fuse components
    @include fuse-countdown-theme($theme);
    @include fuse-search-bar-theme($theme);
    @include fuse-shortcuts-theme($theme);
    @include fuse-sidebar-theme($theme);
    @include fuse-theme-options-theme($theme);
    @include fuse-material-color-picker-theme($theme);
    @include fuse-widget-theme($theme);

    // Theming for 3rd party components
    @include ngx-datatable-theme($theme);
}


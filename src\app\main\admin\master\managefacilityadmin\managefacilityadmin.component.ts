import { Component, ViewChild } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { GridSearchRequestDto } from 'app/dto/common/gridDto';
import { CompanyList } from 'app/dto/companyDto';
import { FacilityGridData, FacilityGridDataResponseDto, ManageFacilityGridData, SaveAdminFacilityRequestDto } from 'app/dto/facilityDto';
import { BaseComponent } from 'app/main/common/base.component';
import { FileuploaderComponent } from 'app/main/common/fileuploader/fileuploader.component';
import { FacilityApiService } from 'app/services/api/facility.api.service.service';
import { environment } from 'environments/environment';
import { Observable } from 'rxjs';
import { CompanyApiService } from 'app/services/api/company.api.service';
import { MatOptionSelectionChange} from '@angular/material';
//import { FacilityAdminPipe } from './facility-admin-pipe';

@Component({
  selector: 'app-manage-facility-admin',
  templateUrl: './managefacilityadmin.component.html',
  styleUrls: ['./managefacilityadmin.component.scss']
})

export class ManagefacilityadminComponent extends BaseComponent {
  @ViewChild('formUser', { static: true }) ngForm;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  search_user = '';
  search_userEmail = '';
  SSOUSERCOMPANYID ='';
  tkn = localStorage.getItem('SSOTOKEN');
  fgFacilityAdmin = new FormGroup({
    facilityAdminId: new FormControl(''),
    email: new FormControl(''),
    facilities:  new FormControl(''),
    admin: new FormControl(true),
    companyControl: new FormControl('')
  });
  selectedRowIndex = 0;
  companyFilteredOptions: Observable<CompanyList[]> | undefined;
  isExistingUpdateMode = false;
  displayedColumns: string[] = ['email', 'facilityName', 'admin', 'edit'];
  dataSource: MatTableDataSource<ManageFacilityGridData>;
  facilityGridData: FacilityGridDataResponseDto[]= [];  
  facilityList: FacilityGridData[] = [];
  checkedOptions: FacilityGridData[];
  length = 50;
  pageSize = 5;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageEvent: PageEvent;
  companyList: CompanyList[];
  show :boolean = true;
  constructor(private _facilityApi: FacilityApiService, private _companyApi: CompanyApiService,
    _snackBar: MatSnackBar, public _dialog: MatDialog, private _router: Router) {
    super(_snackBar);
  }

  ngOnInit() {
    const isAd = localStorage.getItem('SSOADMIN');
    if (isAd == '1' && this.tkn.trim().length > 0) {
    } else {
      localStorage.setItem('SSOUSERNAME', '');
      localStorage.setItem('SSOUSERPIC', '');
      this._router.navigateByUrl('/login');
    }

    //this.getAllFacilities(0);
    this.getCompaniesList();
    this.getFacilityAdminData();   
    const isROLEID = localStorage.getItem('SSOROLEID');
    if(isROLEID != '0')
    {
      this.show = false;
      this.SSOUSERCOMPANYID  = localStorage.getItem('SSOUSERCOMPANYID');
      this.getAllFacilities(this.SSOUSERCOMPANYID);
    }
    this.show = this.show;
  }  

  getCompaniesList(): void {
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: GridSearchRequestDto = {
      page: 0,
      pageSize: this.pageSize,
      searchString: this.search_user,
      UserEmail:this.search_userEmail 
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._companyApi.getCompanies(obj)
      .subscribe(
        (data: any) => {
          this.companyList = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray) {
            const _c: CompanyList = {
              companyId: _obj.companyId,
              companyName: _obj.companyName
            };
            this.companyList.push(_c);
          }
        },
        _error => {
        }
      );
  }

  getfacilityList(event: MatOptionSelectionChange, state: any){
    this.getAllFacilities(event.source.value);
  }
  saveFacilities(): void {
    //console.log(this.fgFacilityAdmin);
    const facilityObj: SaveAdminFacilityRequestDto = {
      admin: (this.fgFacilityAdmin.controls.admin.value== '' || this.fgFacilityAdmin.controls.admin.value == null) ? false : this.fgFacilityAdmin.controls.admin.value,
      //facilityName: this.fgFacilityAdmin.controls.facilityName.value,
      email: this.fgFacilityAdmin.controls.email.value,
      facilityId: (this.fgFacilityAdmin.controls.facilityAdminId.value == '' || this.fgFacilityAdmin.controls.facilityAdminId.value == null) ? 0 : this.fgFacilityAdmin.controls.facilityAdminId.value,
      // facilityName: this.checkedOptions.map(fac => `${fac.code} - ${fac.name}`).toString()
      facilityName: this.checkedOptions.map(fac => `${fac.code}`).toString(),
      facilityIdVal: this.checkedOptions.map(fac => `${fac.id}`).toString(),
      createdby:Number(localStorage.getItem('SSOUSERID'))
    };   

    const obj = this.wrapRequestObject(facilityObj);
    this._facilityApi.saveAdminFacility(obj)
      .subscribe(
        (data) => {
          if (data.code === 1) {
            this.showSuccessMsg('Changes has been saved successfully');           
            this.ngForm.resetForm();
            this.selectedRowIndex = 0;
            this.getFacilityAdminData();
          }  else if (data.code === -1){
            this.showErrorMsg('User email already exists');
          }
          else {
            this.showErrorMsg(data.msg);
          }
        },
        _error => {
        }
      );

  }

  getAllFacilities(companyId): void {
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: GridSearchRequestDto = {
      page: this.paginator.pageIndex,
      pageSize: 500,
      searchString: this.search_userEmail,
      UserEmail:this.search_userEmail
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._facilityApi.getAllFacilities(obj)
      .subscribe(
        (data) => {
          this.facilityList = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray['gridData']['facilitiesResponse']) {
            const _f: FacilityGridData = {
              id: parseInt(_obj.facilityId, 10),
              active: _obj.isActive,
              code: _obj.facilityCode,
              hrms: _obj.isHrmsIntegrated,
              logo: _obj.logo,
              name: _obj.facilityName,
              companyId : _obj.companyId
            };

            this.facilityList.push(_f);
            if(companyId > 0)
{
  this.facilityList = this.facilityList.filter(com => com.companyId== companyId);
}
          }

        },
        _error => {
        }
      );
  }
  
  getFacilityAdminData(): void {
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: GridSearchRequestDto = {
      page: 0,
      pageSize: this.pageSize,
      searchString: this.search_user,
      UserEmail:this.search_userEmail
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._facilityApi.getAllAdminFacilities(obj)
      .subscribe(
        (data) => {
          this.facilityGridData = [];
          const responseData = JSON.parse(data.obj.toString());
          if(responseData){
            this.length = responseData.totalRecords;
            this.facilityGridData = responseData.gridData;          
          }
          else{
            this.length = 0;
            this.facilityGridData = [];          
          }
          this.dataSource = new MatTableDataSource(this.facilityGridData);
          this.dataSource.sort = this.sort;
          this.paginator.firstPage();
        },
        _error => {
        }
      );
  }

  pagerEvent(event?: PageEvent): void {
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: GridSearchRequestDto = {
      page: event.pageIndex,
      pageSize: event.pageSize,
      searchString: this.search_user,
      UserEmail:this.search_userEmail
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._facilityApi.getAllAdminFacilities(obj)
      .subscribe(
        (data) => {
          this.facilityGridData = [];
          const responseData = JSON.parse(data.obj.toString());
          if(responseData){
            this.facilityGridData = responseData.gridData;          
          }
          else{
            this.facilityGridData = [];          
          }
          this.dataSource = new MatTableDataSource(this.facilityGridData);
          this.dataSource.sort = this.sort;
        },
        _error => {
        }
      );
  }

  resetExtraDetails(): void {
    this.search_user = '';
    this.selectedRowIndex = 0;
    this.isExistingUpdateMode = false;
    this.getFacilityAdminData();
  }

  loadAdminFacilityDetails(row) {
    this.checkedOptions = [];
    let facilityArray= [];
    this.isExistingUpdateMode = true;
    this.selectedRowIndex = row.facilityId;
    if(row.facilityName.split(",").length > 0) {
      row.facilityName.split(",").forEach(element => {
        facilityArray.push(element.split(" - ")[0])
      });
    } else {
      facilityArray.push(row.facilityName.split(" - ")[0])
    }
    this.fgFacilityAdmin.controls.facilityAdminId.setValue(row.facilityId);
    //this.fgFacilityAdmin.controls.facilities.setValue(row.facilityName);
    this.fgFacilityAdmin.controls.email.setValue(row.email);
    this.fgFacilityAdmin.controls.admin.setValue(row.admin);
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: GridSearchRequestDto = {
      page: this.paginator.pageIndex,
      pageSize: 500,
      searchString: this.search_userEmail,
      UserEmail:this.search_userEmail
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._facilityApi.getAllFacilities(obj)
      .subscribe(
        (data) => {
          this.facilityList = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray['gridData']['facilitiesResponse']) {
            const _f: FacilityGridData = {
              id: parseInt(_obj.facilityId, 10),
              active: _obj.isActive,
              code: _obj.facilityCode,
              hrms: _obj.isHrmsIntegrated,
              logo: _obj.logo,
              name: _obj.facilityName,
              companyId : _obj.companyId
            };

            this.facilityList.push(_f);
            this.checkedOptions = this.facilityList.filter(array => facilityArray.find(filter => array.code === filter));
            for (const _obj of this.checkedOptions) {
              this.fgFacilityAdmin.controls.companyControl.setValue(_obj.companyId);
              if(_obj.companyId > 0)
              {
                this.facilityList = this.facilityList.filter(com => com.companyId== _obj.companyId);
                this.checkedOptions = this.facilityList.filter(array => facilityArray.find(filter => array.code === filter));
              }
              break;
            }
            if(this.fgFacilityAdmin.controls.companyControl.value > 0)
{
  this.facilityList = this.facilityList.filter(com => com.companyId== this.fgFacilityAdmin.controls.companyControl.value );
  this.checkedOptions = this.facilityList.filter(array => facilityArray.find(filter => array.code === filter));
}
          }

        },
        _error => {
        }
      );
    
  }

  change(event: PageEvent) {
    this.getFacilityAdminData();
  }
  uploadLogo(): boolean {
    const dialogRef = this._dialog.open(FileuploaderComponent, {
      width: '400px',
      data: { 
        name: 'Change Facility Logo', 
        filePath: '', 
        base64: this.fgFacilityAdmin.controls['logo'].value
      }
    });

    dialogRef.afterClosed().subscribe(result => {
         console.log(result.data);
          if (result.data && result.data.length > 0) {
            this.fgFacilityAdmin.controls.logo.setValue(result.data);
          }
          else {
            this.fgFacilityAdmin.controls.logo.setValue(null);
          }
    });
    return false;
  }
   
  navigateToHousekeeping(): void {
    window.location.href = environment.hpbsUrl + '#/login?token=' + this.tkn;
  }

}



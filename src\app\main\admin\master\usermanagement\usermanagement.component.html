<div id="forms" class="page-layout simple fullwidth" fxLayout="column">

    <div class="header accent p-24 h-20" fxLayout="row" fxLayoutAlign="start center">
        <div fxLayout="column" fxLayoutAlign="center start">
            <div fxLayout="row" fxLayoutAlign="start center">
                <h2>User & Access Management</h2>
                <button mat-raised-button style="margin-left: 50px;" color="primary" style="display:none"
                    (click)="navigateToHousekeeping()">Go To Houskeeping</button>
            </div>

        </div>
    </div>
    <div class="content p-24">

        <div class="mb-24" fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="space-around start" fxLayout.gt-md="row">

            <div fxFlex="50" fxLayout="column">
                <form class="mat-card mat-elevation-z4 p-24" fxLayout="column" fxLayoutAlign="start"
                    [formGroup]="fgUser" fxFlex="1 0 auto" name="form" (ngSubmit)="saveUser()" #formUser="ngForm">
                    <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                    <mat-form-field appearance="outline" fxFlex="100" class="pr-4">
                        <mat-label>Select User</mat-label>
                        <mat-select (selectionChange)="onUserSelect($event)">
                            <ngx-mat-select-search [formControl]="usersFilterCtrl" placeholderLabel="Search..."
                                noEntriesFoundLabel="No results found">
                            </ngx-mat-select-search>
                            <mat-option [value]="user.userId" *ngFor="let user of filteredUsersList | async">
                                {{ user.userFullName }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                    </div>
                    <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                        <mat-form-field appearance="outline" fxFlex="100" class="pr-4">
                            <mat-label>Email</mat-label>
                            <input matInput required type="email" formControlName="email"
                                [readonly]="shouldBeReadonly!==''">

                            <mat-icon matSuffix class="secondary-text">email</mat-icon>
                            <mat-error>Email is required!</mat-error>
                        </mat-form-field>
                    </div>
                    <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                        <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                            <mat-label>Full Name</mat-label>
                            <input matInput required type="email" formControlName="name">
                            <mat-icon matSuffix class="secondary-text">account_circle</mat-icon>
                            <mat-error>Full Name is required!</mat-error>
                        </mat-form-field>
                        <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                            <mat-label>Password</mat-label>
                            <input matInput type="password" formControlName="password">
                            <mat-icon matSuffix class="secondary-text">visibility</mat-icon>
                            <mat-error>Password is required!</mat-error>
                        </mat-form-field>
                    </div>
                    <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink" *ngIf=show>
                        <mat-form-field appearance="outline" fxFlex="100" class="pr-4">
                            <mat-label>Company</mat-label>
                            <mat-select required formControlName="companyControl"
                                (selectionChange)="getfacilityList($event)">
                                <mat-option [value]="company.companyId" *ngFor="let company of companyList">
                                    {{company.companyName}}
                                </mat-option>
                            </mat-select>
                            <mat-error>Company is required!</mat-error>
                        </mat-form-field>
                    </div>
                    <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                        <mat-form-field appearance="outline" fxFlex="100" class="pr-4">
                            <mat-label>Allowed Facilities</mat-label>
                            <mat-select formControlName="facilities_hpbs" multiple>
                                <input type="text" #text matInput placeholder="search facility(ies)..."
                                    style="height: 40px;padding: 25px;">
                                <mat-option [value]="fac.id" *ngFor="let fac of facilityList"
                                    [ngStyle]="{ display : (text.value !== '' && fac.code.toLowerCase().indexOf(text.value.toLowerCase()) === -1) ? 'none' : 'flex' }">
                                    <strong>{{fac.code}} &nbsp;</strong> - {{fac.name}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                        <mat-form-field appearance="outline" fxFlex="100" class="pr-4">
                            <mat-label>Enabled Modules</mat-label>
                            <mat-select formControlName="moduleDesc" multiple required>
                                <mat-option [value]="module.moduleId" *ngFor="let module of moduleList">
                                    {{ module.moduleDesc }}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="space-around start" fxLayout.gt-md="row">
                        <div fxFlex="40" fxLayout="column">

                            <div fxLayout="row wrap" fxFlex="100" fxLayoutAlign="start left">
                                <mat-checkbox fxFlex="50" formControlName="admin" disabled
                                    style="display: none;">Admin</mat-checkbox>
                                <mat-checkbox fxFlex="50" formControlName="active">Active</mat-checkbox>
                            </div>
                            <!-- <div fxLayout="row wrap" fxFlex="100" fxLayoutAlign="end left" >
                               
                            </div> -->
                        </div>
                        <div fxFlex="60" fxLayout="column">

                            <div fxLayout="row wrap" fxLayoutAlign="start center" fxFlex="1 0 auto">

                            </div>

                            <div fxLayout="row" fxLayoutAlign="end center" fxFlex="1 0 auto">
                                <button mat-stroked-button type="reset" (click)="resetExtraDetails()"
                                    class="mat-top-margin mr-20" color="primary">Reset</button>
                                <button mat-flat-button type="submit"
                                    [disabled]="fgUser.invalid || selectedRowIndex===0" class="mat-top-margin mr-20"
                                    color="primary">Save
                                    Changes</button>
                                <!-- <button mat-flat-button type="button" class="mat-top-margin" color="primary">Review &
                                    Save Changes</button> -->
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div fxFlex="50" fxLayout="column">
                <div class="mat-card mat-elevation-z4 p-24">
                    <mat-form-field style="width:50%;">
                        <button mat-button matPrefix mat-icon-button>
                            <mat-icon>search</mat-icon>
                        </button>
                        <input matInput type="text" (keyup)="getAllUsers()" [(ngModel)]="search_user"
                            placeholder="Filter Users">
                        <button *ngIf="search_user" mat-button matSuffix mat-icon-button aria-label="Clear"
                            (click)="search_user=''; getAllUsers();">
                            <mat-icon>close</mat-icon>
                        </button>
                    </mat-form-field>
                    <table mat-table [dataSource]="dataSource" matSort>
                        <ng-container matColumnDef="userName">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Email </th>
                            <td mat-cell *matCellDef="let row"> {{row.userName}} </td>
                        </ng-container>
                        <ng-container matColumnDef="userFullName">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Full Name </th>
                            <td mat-cell *matCellDef="let row"> {{row.userFullName}}</td>
                        </ng-container>
                        <ng-container matColumnDef="isActive">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header> Active </th>
                            <td mat-cell *matCellDef="let row"> {{row.isActive===true?'Yes': 'No'}} </td>
                        </ng-container>
                        <!-- <ng-container matColumnDef="edit">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Edit </th>
                            <td mat-cell *matCellDef="let row" [style.color]="">
                                <button mat-stroked-button color="primary" (click)="loadUserDetails(row)" matSuffix
                                    class="mat-sm">
                                    <mat-icon>edit</mat-icon>
                                </button>
                            </td>
                        </ng-container> -->
                        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                        <!-- <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                            [ngClass]="{'highlight': selectedRowIndex == row.userId}">
                        </tr> -->
                        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                    </table>
                    <div *ngIf="!dataSource?.data?.length && search_user" class="noRecordsMessage">
                        <mat-icon>search_off</mat-icon>
                        No matching records found. Please try a different search.
                    </div>
                    <mat-paginator [length]="length" [pageSize]="pageSize" [pageSizeOptions]="pageSizeOptions"
                        (page)="pagerEvent($event)"></mat-paginator>
                </div>
            </div>
        </div>
        <div class="mb-24" fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="space-around start" fxLayout.gt-md="row">


        </div>
    </div>
</div>
@import "src/@fuse/scss/fuse";

toolbar {
    position: relative;
    display: flex;
    flex: 0 0 auto;
    z-index: 4;

    &.below {
        z-index: 2;
    }

    .mat-toolbar {
        position: relative;
        background: inherit !important;
        color: inherit !important;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 
            0 1px 2px -1px rgba(0, 0, 0, 0.1) !important;

    }

    .logo {
        display: flex;
        align-items: center;

        .logo-icon {
            width: 120px;
        }
    }

    .user-button,
    fuse-search-bar,
    .language-button,
    .chat-panel-toggle-button,
    .quick-panel-toggle-button {
        min-width: 64px;
        height: 64px;

        @include media-breakpoint('xs') {
            height: 56px;
        }
    }

    .navbar-toggle-button {
        min-width: 56px;
        height: 56px;
    }

    .toolbar-separator {
        height: 64px;
        width: 1px;

        @include media-breakpoint('xs') {
            height: 56px;
        }
    }

    .search-container {
        margin-right: 16px;

        .search-toggle-button {
            min-width: 48px;
            height: 48px;
            transition: all 0.3s ease-in-out;

            &:hover {
                background-color: rgba(0, 0, 0, 0.04);
            }
        }

        .search-input-container {
            animation: slideInFromRight 0.3s ease-out;

            .search-input {
                width: 300px;
                margin-right: 8px;

                .mat-form-field-wrapper {
                    padding-bottom: 0;
                }

                .mat-form-field-infix {
                    border-top: 0;
                    padding: 8px 0;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .mat-form-field-outline {
                    top: 0;
                }

                // .mat-form-field-outline-start,
                // .mat-form-field-outline-end {
                //     border-radius: 20px;
                // }

                .mat-form-field-outline-thick {
                    .mat-form-field-outline-start,
                    .mat-form-field-outline-end {
                        border-width: 1px;
                    }
                }

                input {
                    font-size: 16px;
                }
            }

            .search-close-button {
                min-width: 40px;
                height: 40px;
                transition: all 0.3s ease-in-out;

                &:hover {
                    background-color: rgba(0, 0, 0, 0.04);
                }
            }
        }
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

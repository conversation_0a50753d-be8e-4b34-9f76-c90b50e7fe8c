@import 'src/@fuse/scss/fuse';

fuse-navigation {
    display: flex;
    flex: 1 0 auto;

    >.nav {
        margin: 0;
        padding: 0;
        width: 100%;
    }

    .nav.horizontal {
        display: flex;
        flex-direction: column !important;
        padding: 10px 0 10px 10px;
    }

    .nav.horizontal>.nav-item>.nav-link {
        height: 46px;
        border-radius: 4px;
    }

    .nav.horizontal>.nav-item>.nav-link.primary {
        background-color: #2C3344 !important;
        color: white !important;
    }

    .nav.horizontal>.nav-item:hover {
        background-color: #2C3344 !important;
        color: white !important;
        border-radius: 4px;
    }
        .nav.horizontal>.nav-item{
        color: #b6b6b6 !important;
    }
}
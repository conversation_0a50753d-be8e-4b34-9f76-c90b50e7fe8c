import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ApiRequest, ApiResponse } from 'app/dto/common/commonDto';
import { Observable } from 'rxjs';
import { BaseApiServiceService } from './baseapi.service';

@Injectable({
  providedIn: 'root'
})
export class RoleService  extends BaseApiServiceService {


  constructor(public http: HttpClient, public router: Router) {
    super();
  }

  getAllRoles(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}role/list`,
        {
          'data': request
        });
  }
  getActiveRoleList(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}role/getactiverolelist`,
        {
          'data': request
        });
  }
  

  getRole(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}role/get`,
        {
          'data': request
        });
  }

  saveRole(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}role/save`,
        {
          'data': request
        });
  }
  
  deleteRole(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}role/delete`,
        {
          'data': request
        });
  }

}

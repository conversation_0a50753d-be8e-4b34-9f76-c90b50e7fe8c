:host {

    .folded:not(.unfolded) & {

        > .group-title {
            align-items: center;

            > span {
                opacity: 0;
                transition: opacity 200ms ease;
            }

            &:before {
                content: '';
                display: block;
                position: absolute;
                min-width: 1.6rem;
                border-top: 2px solid;
                opacity: 0.2;
            }
        }
    }
}

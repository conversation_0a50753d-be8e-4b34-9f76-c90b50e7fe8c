import { Injectable } from '@angular/core';
import { BaseApiServiceService } from './baseapi.service';
import { Router } from '@angular/router';
import { ApiResponse, ApiRequest } from 'app/dto/common/commonDto';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class FacilityApiService extends BaseApiServiceService {

  constructor(public http: HttpClient, public router: Router) {
    super();
  }

  getAdminLists(): Observable<ApiResponse> {
    return this
      .http
      .get<ApiResponse>(`${this.baseUrl}/facility/adminlist`);
  }

  getAllFacilities(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}facility/loadallfacilities`,
        {
          'data': request
        });
  }

  getFacilitiesList(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}facility/facilitylist`,
        {
          'data': request
        });
  }

  

  saveFacility(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}facility/savefacility`,
        {
          'data': request
        });
  }

  saveFacilityList(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}facility/savefacilityList`,
        {
          'data': request
        });
  }

  getAllAdminFacilities(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}facility/adminfacility`,
        {
          'data': request
        });
  }

  saveAdminFacility(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}facility/saveadminfacility`,
        {
          'data': request
        });
  }

  getActiveFacilityList(): Observable<ApiResponse> {
    return this
      .http
      .get<ApiResponse>(`${this.baseUrl}/facility/getactivefacilitylist`);
  }
}

import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
    selector: 'app-resignation-date-dialog',
    template: `
        <div class="p-24">
            <h2 mat-dialog-title>Confirm Staff Resignation</h2>
            <form [formGroup]="form" (ngSubmit)="onSubmit()">
                <mat-dialog-content>
                    <p class="mb-16">Are you sure you want to delete this staff member?</p>
                    <mat-form-field appearance="outline" class="w-100">
                        <mat-label>Resignation Date</mat-label>
                        <input matInput [matDatepicker]="picker" formControlName="resignationDate" required>
                        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                        <mat-error *ngIf="form.get('resignationDate').hasError('required')">
                            Resignation date is required
                        </mat-error>
                    </mat-form-field>
                </mat-dialog-content>
                <mat-dialog-actions align="end">
                    <button mat-button type="button" (click)="onCancel()">Cancel</button>
                    <button mat-flat-button color="warn" type="submit" [disabled]="form.invalid">Confirm</button>
                </mat-dialog-actions>
            </form>
        </div>
    `,
    styles: [`
        .w-100 {
            width: 100%;
            display: inline !important;
        }
        mat-dialog-content {
            min-width: 300px;
        }
        .p-24 {
            padding: 24px;
        }
        .mb-16 {
            margin-bottom: 16px;
        }
    `]
})
export class ResignationDateDialogComponent {
    form: FormGroup;

    constructor(
        private _formBuilder: FormBuilder,
        public dialogRef: MatDialogRef<ResignationDateDialogComponent>,
        @Inject(MAT_DIALOG_DATA) public data: any
    ) {
        this.form = this._formBuilder.group({
            resignationDate: ['', Validators.required]
        });
    }

    onSubmit(): void {
        if (this.form.valid) {
            this.dialogRef.close(this.form.get('resignationDate').value);
        }
    }

    onCancel(): void {
        this.dialogRef.close();
    }
} 
:host {

    .content {

        form {
            width: 100%;
            // max-width: 800px !important;
        }

        .form-errors-model {
            flex: 1;

            code {
                background: none !important;
            }
        }

        .horizontal-stepper-wrapper,
        .vertical-stepper-wrapper {
            max-width: 800px;
        }

        .mat-stroked-button.mat-small {

            height: 25px;
            line-height: 20px;
            min-height: 20px;
            vertical-align: middle !important;
            font-size: 10px;
            margin-bottom: 33%;
        }

        .mat-stroked-button.mat-sm {

            height: 25px;
            line-height: 20px;
            min-height: 20px;
            vertical-align: middle !important;
            font-size: 10px;
        }

        .mat-flat-button.mat-top-margin {
            margin-top: 20px !important;
        }

        .mat-stroked-button.mat-top-margin {
            margin-top: 20px !important;
        }

        .h-30 {
            height: 30px !important;
            min-height: 30px;
        }

        table {
            width: 100%;
        }

        .mat-form-field.grid-search-field {
            // font-size: 14px;
            width: 50%;
            margin-left: 20px !important;
        }

        .blue-snackbar {
            background: #2196F3;
        }

    }
}
.noRecordsMessage {
    text-align: center;
    padding: 20px;
    color: #666;
    font-size: 16px;
}

.noRecordsMessage mat-icon {
    vertical-align: middle;
    margin-right: 8px;
}
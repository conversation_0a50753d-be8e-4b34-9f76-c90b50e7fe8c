import { mstUserInstance, mstUserApp } from './saveUserDto';

export class EmailResponseDto {
    emailId: number;
    email: string;   
    isActive: boolean;
    isDeleted: boolean;
}

export class emailGridData {
    emailId: number;
    email: string;   
    isActive: boolean;
    isDeleted: boolean;
}

export class emailList {
    emailId: number;
    email: string;
}

export interface SaveEmailRequestDto {
    emailId: number;
    email: string;   
    isActive: boolean;
    isDeleted: boolean;
    createdby:number;
}
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'facilityAdmin',
  pure: false
})
export class FacilityAdminPipe implements PipeTransform
{
    transform(value: string, args: any[]): string 
    {
        if (value === null) {return "";}
        const adminLists = value.split(',');
        const adminNameLists =[];
        for (let i = 0; i < adminLists.length; i++)
        {
          const admins = adminLists[i].split(' - ')[1];
          adminNameLists.push(admins);
        }
        return adminNameLists.toString().replace(/,/g,", ");
    }
}
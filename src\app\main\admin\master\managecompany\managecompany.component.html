<div id="forms" class="page-layout simple fullwidth" fxLayout="column">
    <div class="header accent p-24 h-20" fxLayout="row" fxLayoutAlign="start center">
        <div fxLayout="column" fxLayoutAlign="center start">
            <div fxLayout="row" fxLayoutAlign="start center">
                <h2>Manage Company</h2>
                <!-- <button mat-raised-button style="margin-left: 50px;" color="primary" (click)="navigateToHousekeeping()">Go To Houskeeping</button>  -->
            </div>
        </div>
    </div>
    <div class="content p-24">
        <div class="mb-24" fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="space-around start" fxLayout.gt-md="row">
            <div fxFlex="100" fxLayout="column">
                <div fxFlex="100" fxLayout="column" class="mt-20">
                    <div fxLayout="row wrap" fxLayoutGap="10px" fxLayoutAlign="start" fxLayout.gt-md="row wrap">
                        <div fxFlex="40" fxLayout="column" style="align-items: center">
                            <form class="mat-card mat-elevation-z4 p-24" fxLayout="column" fxLayoutAlign="start"
                                [formGroup]="fgUser" fxFlex="1 0 shrink" name="form" (ngSubmit)="saveCompany()"
                                #formUser="ngForm">
                                <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                                    <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                                        <mat-label>Company Name</mat-label>
                                        <input matInput required type="text" formControlName="companyName">
                                        <mat-icon matSuffix class="secondary-text">account_circle</mat-icon>
                                        <mat-error>Company Name is required!</mat-error>
                                    </mat-form-field>
                                    <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                                        <mat-label>Effective Date</mat-label>
                                        <input matInput [matDatepicker]="effectiveDatePicker"
                                            placeholder="Effective Date" formControlName="effectiveDate" required>
                                        <mat-datepicker-toggle matSuffix
                                            [for]="effectiveDatePicker"></mat-datepicker-toggle>
                                        <mat-datepicker #effectiveDatePicker></mat-datepicker>
                                    </mat-form-field>
                                </div>
                                <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                                    <mat-form-field appearance="outline" fxFlex="90" class="pr-4">
                                        <mat-label>Contract Expiry Date</mat-label>
                                        <input matInput [matDatepicker]="expiryDatePicker"
                                            placeholder="Contract Expiry Date" formControlName="contractExpiryDate">
                                        <mat-datepicker-toggle matSuffix
                                            [for]="expiryDatePicker"></mat-datepicker-toggle>
                                        <mat-datepicker #expiryDatePicker></mat-datepicker>
                                    </mat-form-field>
                                    <mat-form-field appearance="outline" fxFlex="90" class="pr-4">
                                        <mat-label>Country</mat-label>
                                        <input type="text" required placeholder="Select Country" aria-label="Country"
                                            matInput formControlName="countryControl" [matAutocomplete]="auto">
                                        <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete">
                                            <mat-option *ngFor="let country of countryFilteredOptions | async"
                                                [value]="country.countryName">
                                                {{country.countryName}}
                                            </mat-option>
                                        </mat-autocomplete>
                                        <mat-error>Country is required!</mat-error>
                                    </mat-form-field>
                                </div>
                                <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                                    <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                                        <mat-label>Country Code</mat-label>
                                        <input matInput required type="number" formControlName="countryCode">
                                        <mat-error>Country Code is required!</mat-error>
                                    </mat-form-field>
                                    <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                                        <mat-label>SMS URL</mat-label>
                                        <input matInput required type="text" formControlName="smsUrl">
                                        <mat-error>SMS URL is required!</mat-error>
                                    </mat-form-field>
                                </div>
                                <div fxLayout="row" fxLayoutAlign="start" fxFlex="1 0 shrink">
                                    <mat-form-field appearance="outline" fxFlex="100" class="pr-4">
                                        <mat-label>Company logo</mat-label>
                                        <input matInput readonly="readonly" formControlName='logo'>
                                        <button mat-stroked-button color="primary" (click)="uploadLogo()" matSuffix
                                            class="mat-small">Upload</button>
                                    </mat-form-field>

                                </div>
                                <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                                    <mat-form-field appearance="outline" fxFlex="100" class="pr-4">
                                        <mat-label>Enabled Modules</mat-label>
                                        <mat-select formControlName="moduleDesc" multiple [(ngModel)]="checkedOptions"
                                            required>
                                            <mat-option [value]="module" *ngFor="let module of moduleList">
                                                {{ module.moduleDesc }}
                                            </mat-option>
                                        </mat-select>
                                    </mat-form-field>
                                </div>

                                <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                                    <div fxFlex="100" fxLayoutAlign="start">
                                        <mat-checkbox fxFlex="50" formControlName="active">Active</mat-checkbox>
                                    </div>
                                </div>

                                <div fxLayout="row" fxLayoutAlign="end " fxFlex="1 0 shrink">
                                    <button mat-stroked-button type="reset" (click)="resetExtraDetails()"
                                        class="mat-top-margin mr-20" color="primary">Reset</button>
                                    <button mat-flat-button type="submit" [disabled]="fgUser.invalid"
                                        class="mat-top-margin mr-20" color="primary">Save
                                        Changes</button>
                                </div>
                            </form>
                        </div>
                        <div fxFlex="55" fxLayout="column">
                            <div class="mat-card mat-elevation-z4 p-24">
                                <mat-form-field style="width:50%;">
                                    <button mat-button matPrefix mat-icon-button>
                                        <mat-icon>search</mat-icon>
                                    </button>
                                    <input matInput type="text" (keyup)="getAllCompanies()" [(ngModel)]="search_user"
                                        placeholder="Filter Company">
                                    <button *ngIf="search_user" mat-button matSuffix mat-icon-button aria-label="Clear"
                                        (click)="search_user=''; getAllCompanies();">
                                        <mat-icon>close</mat-icon>
                                    </button>
                                </mat-form-field>
                                <table mat-table [dataSource]="dataSource" matSort>
                                    <ng-container matColumnDef="companyName">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Company Name </th>
                                        <td mat-cell *matCellDef="let row"> {{row.companyName}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="effectiveDate">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Effective Date </th>
                                        <td mat-cell *matCellDef="let row"> {{row.effectiveDate | date:'dd-MMM-yyyy' }}
                                        </td>
                                    </ng-container>
                                    <ng-container matColumnDef="country">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Country </th>
                                        <td mat-cell *matCellDef="let row"> {{row.country}}</td>
                                    </ng-container>
                                    <ng-container matColumnDef="companyLogo">
                                        <th mat-header-cell *matHeaderCellDef> Company Logo </th>
                                        <td mat-cell *matCellDef="let row"><img height="48px" width="48px"
                                                [src]=row.companyLogo />
                                        </td>
                                    </ng-container>
                                    <ng-container matColumnDef="contractExpiryDate">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Expirty Date </th>
                                        <td mat-cell *matCellDef="let row"> {{row.contractExpiryDate |
                                            date:'dd-MMM-yyyy'}}
                                        </td>
                                    </ng-container>
                                    <ng-container matColumnDef="moduleInst">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Enabled Modules </th>
                                        <td mat-cell *matCellDef="let row"> {{ getModuleDesc(row.moduleInst) }}</td>
                                    </ng-container>
                                    <ng-container matColumnDef="active">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header> Active </th>
                                        <td mat-cell *matCellDef="let row"> {{row.active===true?'Yes': 'No'}} </td>
                                    </ng-container>
                                    <ng-container matColumnDef="edit">
                                        <th mat-header-cell *matHeaderCellDef mat-sort-header>Edit </th>
                                        <td mat-cell *matCellDef="let row" [style.color]="">
                                            <button mat-stroked-button color="primary" (click)="loadCompanyDetails(row)"
                                                matSuffix class="mat-sm">
                                                <mat-icon>edit</mat-icon>
                                            </button>
                                        </td>
                                    </ng-container>
                                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                                        [ngClass]="{'highlight': selectedRowIndex == row.userId}">
                                    </tr>
                                </table>
                                <div *ngIf="!dataSource?.data?.length && search_user" class="noRecordsMessage">
                                    <mat-icon>search_off</mat-icon>
                                    No matching records found. Please try a different search.
                                </div>
                                <mat-paginator [length]="length" [pageSize]="pageSize"
                                    [pageSizeOptions]="pageSizeOptions" (page)="pagerEvent($event)"></mat-paginator>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
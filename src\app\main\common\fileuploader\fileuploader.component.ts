import { Component, ElementRef, OnInit, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';


@Component({
  selector: 'app-fileuploader',
  templateUrl: './fileuploader.component.html',
  styleUrls: ['./fileuploader.component.scss']
})
export class FileuploaderComponent implements OnInit {
  @ViewChild("fileUpload", { static: false}) fileUpload: ElementRef;
  imageError: string;
  isImageSaved: boolean;
  cardImageBase64: string;
  imageUrl = '';

  
  constructor(
    public dialogRef: MatDialogRef<FileuploaderComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData) {}

    ngOnInit(): void {
      this.imageUrl=this.data.base64;
    }

  onNoClick(): void {
    this.cardImageBase64 = this.data["base64"]
    this.dialogRef.close();
  }

  uploadFileTemplate() {
    this.fileUpload.nativeElement.click();
  }
  fileSelected(fileInput: any) {
    this.imageError = null;
    if (fileInput.target.files && fileInput.target.files[0]) {
        // Size Filter Bytes
        const max_size = 20971520;
        const allowed_types = ['image/png', 'image/jpeg'];
        const max_height = 15200;
        const max_width = 25600;

        if (fileInput.target.files[0].size > max_size) {
            this.imageError =
                'Maximum size allowed is ' + max_size / 1000 + 'Mb';

            return false;
        }
        
        const reader = new FileReader();
        reader.onload = (e: any) => {
            const image = new Image();
            image.src = e.target.result;
            image.onload = rs => {
                const img_height = rs.currentTarget['height'];
                const img_width = rs.currentTarget['width'];

                console.log(img_height, img_width);


                if (img_height > max_height && img_width > max_width) {
                    this.imageError =
                        'Maximum dimentions allowed ' +
                        max_height +
                        '*' +
                        max_width +
                        'px';
                    return false;
                } else {
                    const imgBase64Path = e.target.result;
                    this.cardImageBase64 = imgBase64Path;
                    this.isImageSaved = true;
                    // this.previewImagePath = imgBase64Path;
                    this.dialogRef.close({data: this.cardImageBase64});
                }
            };
        };

        reader.readAsDataURL(fileInput.target.files[0]);
    }
  }

    removeImage() {
      this.data["base64"] = null;
        this.cardImageBase64 = null;
        this.isImageSaved = false;
        this.dialogRef.close({data: this.cardImageBase64});
    }
}

export interface DialogData {
  filePath: string;
  name: string;
  base64: string;
}
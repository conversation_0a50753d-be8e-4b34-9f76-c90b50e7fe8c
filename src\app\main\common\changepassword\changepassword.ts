import { Component, ElementRef, OnInit, Inject, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { OldPwdValidators } from './pwd.validators';
import { SaveUserRequestDto, mstUserApp, mstUserInstance, mstUserModule } from 'app/dto/saveUserDto';
import { UserApiService } from 'app/services/api/user.api.service';
import { BaseComponent } from 'app/main/common/base.component';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-changepassword',
  templateUrl: './changepassword.html',
  styleUrls: ['./changepassword.scss']
})
export class changepasswordComponent extends BaseComponent {
  form1: FormGroup; 
  search_userId = 0;
  search_userEmail = '';
  dataSource: MatTableDataSource<form1Dto>;
  constructor( private _userApi: UserApiService,fb: FormBuilder,public dialogRef: MatDialogRef<changepasswordComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData,_snackBar: MatSnackBar)
    {
      super(_snackBar);
    this.form1 = fb.group({
      'oldPwd': ['',Validators.required],
      'newPwd': ['',Validators.required],
      'confirmPwd': ['',Validators.required]
    }, {
      validator: OldPwdValidators.matchPwds
    });
    
    }
    ngOnInit(): void {
    }
    get oldPwd(){
      return this.form1.get('oldPwd');
    }
    get newPwd(){
      return this.form1.get('newPwd');
    }
    get confirmPwd(){
      return this.form1.get('confirmPwd');
    }
    changepassword(): void {
      //console.log(this.fgUser);
      this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
      this.search_userId = parseInt(localStorage.getItem('SSOUSERID'));
      const userObj: SaveUserRequestDto = {
        isActive: true,
        fullName: this.form1.controls.oldPwd.value,
        password: this.form1.controls.newPwd.value,
        email: this.search_userEmail ,
        userName: 'changepassword',
        userId: this.search_userId,
        userApps: [],
        userInsts: [],
        userModules: [],
        isAdmin: true,
        //isAdmin: false,
        createdby:Number(localStorage.getItem('SSOUSERID'))
      };    

      const obj = this.wrapRequestObject(userObj);
      this._userApi.saveUser(obj)
        .subscribe(
          (data) => {
            if (data.code === 1) {
              this.showSuccessMsg('Password changed has been saved successfully');
              this.dialogRef.close();
            }  else if (data.code === -1){
              this.showErrorMsg('Current password is not match');
            }
            else {
              this.showErrorMsg(data.msg);
            }
          },
          _error => {
          }
        );
  
    }
  }

export interface DialogData {
    filePath: string;
    name: string;
  }
  
export class form1Dto {
  // export class UserResponseDto {
  userId: number;
  userName: string;
  userFullName: string;
  email: string;
  isActive: boolean;
  isExternalLogin: boolean;
  isAdmin: boolean;
  Edit: number;
}
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { merge, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { FuseNavigationService } from '@fuse/components/navigation/navigation.service';

@Component({
    selector: 'fuse-navigation',
    templateUrl: './navigation.component.html',
    styleUrls: ['./navigation.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class FuseNavigationComponent implements OnInit {
    @Input()
    layout = 'vertical';

    @Input()
    navigation: any;

    roleId: string;

    // Private
    private _unsubscribeAll: Subject<any>;

    /**
     *
     * @param {ChangeDetectorRef} _changeDetectorRef
     * @param {FuseNavigationService} _fuseNavigationService
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseNavigationService: FuseNavigationService
    ) {
        // Set the private defaults
        this._unsubscribeAll = new Subject();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        this.roleId = localStorage.getItem('SSOROLEID');
        // Load the navigation either from the input or from the service
        this.navigation = this.navigation || this._fuseNavigationService.getCurrentNavigation();

        // Subscribe to the current navigation changes
        this._fuseNavigationService.onNavigationChanged
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(() => {

                var currentNavigation = this._fuseNavigationService.getCurrentNavigation()[0];
                var childrens = [];
                if(currentNavigation.roleIds.includes(this.roleId))
                {
                
                currentNavigation.children.forEach(entry => {
                    if (entry.roleIds.includes(this.roleId)) {
                        childrens.push({
                            classes: entry.classes,
                            icon: entry.icon,
                            id: entry.id,
                            roleIds: entry.roleIds,
                            title: entry.title,
                            type: entry.type,
                            url: entry.url,
                        });
                    }
                });
            }
            else{
                currentNavigation =[];
            }
                
                var currentNavigation1 = this._fuseNavigationService.getCurrentNavigation()[1];

                var childrens1 = [];
                if(currentNavigation1.roleIds.includes(this.roleId))
                {
                currentNavigation1.children.forEach(entry => {
                    if (entry.roleIds.includes(this.roleId)) {
                        childrens1.push({
                            classes: entry.classes,
                            icon: entry.icon,
                            id: entry.id,
                            roleIds: entry.roleIds,
                            title: entry.title,
                            type: entry.type,
                            url: entry.url,
                        });
                    }
                });
            }
                
                var newNavigation = [{
                    icon: currentNavigation.icon,
                    id: currentNavigation.id,
                    title: currentNavigation.title,
                    translate: currentNavigation.translate,
                    type: currentNavigation.type,
                    children: childrens
                },{
                    icon: currentNavigation1.icon,
                    id: currentNavigation1.id,
                    title: currentNavigation1.title,
                    translate: currentNavigation1.translate,
                    type: currentNavigation1.type,
                    children: childrens1
                }];
                // Load the navigation
                this.navigation = newNavigation;
                // Mark for check
                this._changeDetectorRef.markForCheck();
            });

        // Subscribe to navigation item
        merge(
            this._fuseNavigationService.onNavigationItemAdded,
            this._fuseNavigationService.onNavigationItemUpdated,
            this._fuseNavigationService.onNavigationItemRemoved
        ).pipe(takeUntil(this._unsubscribeAll))
            .subscribe(() => {

                // Mark for check
                this._changeDetectorRef.markForCheck();
            });
    }
}

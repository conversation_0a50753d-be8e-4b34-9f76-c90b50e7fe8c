import { FuseNavigation } from '@fuse/types';

export const navigation: FuseNavigation[] = [
    // {
    //     id       : 'master',
    //     title    : 'Master',
    //     icon     : 'settings',
    //     translate: 'NAV.MASTER',
    //     type     : 'collapsable',
    //     roleIds  : '0,1,2',
    //     children : [   
           {
               id       : 'test',
               title    : 'Dashboard',
                // translate: 'NAV.Company',
               type     : 'item',
               icon     : 'dashboard',
               url      : '/app/dashboard',
               classes  : 'submenu',
               roleIds  : '0,1,2,3'
                // badge    : {
                //     title    : '25',
                //     translate: 'NAV.SAMPLE.BADGE',
                //     bg       : '#F44336',
                //     fg       : '#FFFFFF'
                // }
           },         
            {
                id       : 'test',
                title    : 'Manage Company',
                //translate: 'NAV.Company',
                type     : 'item',
                icon     : 'business',
                url      : '/app/company',
                classes  : 'submenu',
                roleIds  : '0'
                // badge    : {
                //     title    : '25',
                //     translate: 'NAV.SAMPLE.BADGE',
                //     bg       : '#F44336',
                //     fg       : '#FFFFFF'
                // }
            },
            {
                id       : 'test',
                title    : 'Manage Facility',
                //translate: 'NAV.Company',
                type     : 'item',
                icon     : 'supervised_user_circle',
                url      : '/app/facility',
                classes  : 'submenu',
                roleIds  : '0,1'
                // badge    : {
                //     title    : '25',
                //     translate: 'NAV.SAMPLE.BADGE',
                //     bg       : '#F44336',
                //     fg       : '#FFFFFF'
                // }
            },
            {
                id       : 'test1',
                title    : 'Manage Facility Admin',
                //translate: 'NAV.Company',
                type     : 'item',
                icon     : 'supervised_user_circle',
                url      : '/app/facilityAdmin',
                classes  : 'submenu',
                roleIds  : '0,1' 
                // badge    : {
                //     title    : '25',
                //     translate: 'NAV.SAMPLE.BADGE',
                //     bg       : '#F44336',
                //     fg       : '#FFFFFF'
                // }
            },
            {
                id       : 'test',
                title    : 'Manage Superadministrators',
                //translate: 'NAV.Company',
                type     : 'item',
                icon     : 'supervised_user_circle',
                url      : '/app/email',
                classes  : 'submenu',
                roleIds  : '0' 
                // badge    : {
                //     title    : '25',
                //     translate: 'NAV.SAMPLE.BADGE',
                //     bg       : '#F44336',
                //     fg       : '#FFFFFF'
                // }
            },
            {
                id       : 'sample',
                title    : 'Manage Role',
                // translate: 'NAV.USERMGMT',
                type     : 'item',
                icon     : 'supervised_user_circle',
                url      : '/app/role',
                classes  : 'submenu',
                roleIds  : '0,1,2' 
                // badge    : {
                //     title    : '25',
                //     translate: 'NAV.SAMPLE.BADGE',
                //     bg       : '#F44336',
                //     fg       : '#FFFFFF'
                // }
            },
            {
                id       : 'sample',
                title    : 'Manage Staff',
                type     : 'item',
                icon     : 'people',
                url      : '/app/staff',
                classes  : 'submenu',
                roleIds  : '0,1,2' 
            },
            {
                id       : 'sample',
                title    : 'User & Access Management',
                // translate: 'NAV.USERMGMT',
                type     : 'item',
                icon     : 'supervised_user_circle',
                url      : '/app/user',
                classes  : 'submenu',
                roleIds  : '0,1,2' 
            },
        //                {
        //        id       : 'test',
        //        title    : 'Manage Module',
        //         // translate: 'NAV.Company',
        //        type     : 'item',
        //        icon     : 'dashboard',
        //        url      : '/app/dashboard',
        //        classes  : 'submenu',
        //        roleIds  : '0,1,2,3'
        //    }, 
        // ]
    // }
    // {
    //     id       : 'Home',
    //     title    : 'dashboard',
    //     icon     : 'dashboard',
    //     translate: 'Home',
    //     type     : 'collapsable',
    //     url      : '/app/dashboard',
    //     roleIds  : '0,1,2,3',
    //     children : [   
    //        {
    //             id       : 'test',
    //             title    : 'Dashboard',
    //             translate: 'NAV.Company',
    //             type     : 'item',
    //             icon     : 'dashboard',
    //             url      : '/app/dashboard',
    //             classes  : 'submenu',
    //             roleIds  : '0,1,2,3'
    //             // badge    : {
    //             //     title    : '25',
    //             //     translate: 'NAV.SAMPLE.BADGE',
    //             //     bg       : '#F44336',
    //             //     fg       : '#FFFFFF'
    //             // }
    //         }
    //     ]
    // }
];

// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
    production: false,
    hmr: false,
    // apiUrl: 'https://uetracksg.com/housekeeping/ssoapi/api/',
    hpbsUrl: 'https://uetracksg.com/housekeeping/web/',


    //apiUrl: 'https://sghpbs.azurewebsites.net/hpbs/sso_uat/api/',
    // apiUrl: 'https://uetracksg.com/uetracksso/api/api/',
    apiUrl: 'https://projects.sustainedgeconsulting.com/UEMS/EntryPassSSOApi/api/',
    // apiUrl: 'http://localhost/SSO.Api/api/',

    
    //apiUrl: 'http://localhost:63200/api/',
    //hpbsUrl: 'http://localhost:4200/',
    loginBGUrl: '/assets/images/backgrounds/AboveClouds.png',
    hosUrl: 'https://entrypass.azurewebsites.net/Hospitality/Web',
    entrypassUrl: 'https://uetracksg.com/entrypassv2/prodweb'
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.

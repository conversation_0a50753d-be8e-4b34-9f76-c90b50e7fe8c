<div id="forms" class="page-layout simple fullwidth" fxLayout="column">

    <div class="header accent p-24 h-20" fxLayout="row" fxLayoutAlign="start center">
        <div fxLayout="column" fxLayoutAlign="center start">
            <div fxLayout="row" fxLayoutAlign="start center">
                <h2>Staff Management</h2>
            </div>
        </div>
        <div fxFlex></div>
        <div fxLayout="row" fxLayoutGap="16px">
            <button mat-stroked-button class="master-button" (click)="downloadImportTemplate()">
                <mat-icon>get_app</mat-icon>
                Download Template
            </button>
            <button mat-stroked-button class="master-button" [disabled]="!fgStaff.get('facility').value"
                (click)="importStaff()">
                <mat-icon>cloud_upload</mat-icon>
                Import Staff
            </button>
            <button mat-stroked-button class="master-button" [disabled]="!fgStaff.get('facility').value"
                (click)="exportStaff()">
                <mat-icon>get_app</mat-icon>
                Export Staff
            </button>
        </div>
    </div>
    <div class="content p-24">

        <div class="mb-24" fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="space-around start" fxLayout.gt-md="row">

            <div fxFlex="45" fxLayout="column">
                <form class="mat-card mat-elevation-z4 p-24" fxLayout="column" fxLayoutAlign="start"
                    [formGroup]="fgStaff" fxFlex="1 0 auto" name="form" (ngSubmit)="saveStaff()" #formStaff="ngForm">
                    <!-- Row 1: Facility (single row) -->
                    <div fxLayout="row" fxLayoutAlign="start" fxFlex="1 0 shrink">
                        <mat-form-field appearance="outline" fxFlex="100" class="pr-4">
                            <mat-label>Select Facility</mat-label>
                            <mat-select required formControlName="facility" (selectionChange)="onFacilityChange()">
                                <ngx-mat-select-search [formControl]="facilityFilterCtrl" placeholderLabel="Search..."
                                    noEntriesFoundLabel="No results found">
                                </ngx-mat-select-search>
                                <mat-option *ngFor="let facility of filteredFacilityList | async"
                                    [value]="facility.code">
                                    {{facility.value}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <!-- Row 2: Employee ID (+ Edit) & Employee Name -->
                    <div fxLayout="row" fxLayoutAlign="start" fxFlex="1 0 shrink" fxLayoutGap="16px">
                        <mat-form-field appearance="outline" fxFlex="40" class="pr-4">
                            <mat-label>Employee ID</mat-label>
                            <input matInput required formControlName="employeeId"
                                [attr.disabled]="!fgStaff.get('facility').value || !isEmployeeIdEditable">
                            <mat-icon matSuffix class="secondary-text">badge</mat-icon>
                            <mat-error>Employee ID is required!</mat-error>
                        </mat-form-field>
                        <mat-checkbox fxFlex="7" class="mat-top-margin" [(ngModel)]="isEmployeeIdEditable"
                            [ngModelOptions]="{standalone: true}" (change)="onEmployeeIdEditChange()"
                            matTooltip="Enable Employee ID editing">
                            Edit
                        </mat-checkbox>
                        <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                            <mat-label>Employee Name</mat-label>
                            <input matInput required formControlName="employeeName"
                                [disabled]="!fgStaff.get('facility').value">
                            <mat-icon matSuffix class="secondary-text">person</mat-icon>
                            <mat-error>Employee Name is required!</mat-error>
                        </mat-form-field>
                    </div>
                    <!-- Row 3: Email Address & Contact Number (country code separated) -->
                    <div fxLayout="row" fxLayoutAlign="start" fxFlex="1 0 shrink" fxLayoutGap="16px">
                        <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                            <mat-label>Email Address</mat-label>
                            <input matInput type="email" formControlName="emailAddress">
                            <mat-icon matSuffix class="secondary-text">email</mat-icon>
                            <mat-error *ngIf="fgStaff.get('emailAddress').errors?.required">Email Address is required.
                            </mat-error>
                            <mat-error *ngIf="fgStaff.get('emailAddress').errors?.email">Email Address is not
                                valid.</mat-error>
                        </mat-form-field>
                        <div fxLayout="row" fxFlex="50" fxLayoutGap="8px">
                            <mat-form-field appearance="outline" fxFlex="30" class="pr-2">
                                <mat-label>Code</mat-label>
                                <mat-select formControlName="countryCode" required>
                                    <mat-option [value]="'+65'">
                                        +65
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                            <mat-form-field appearance="outline" fxFlex="70">
                                <mat-label>Contact Number</mat-label>
                                <input matInput required formControlName="phoneNumber"
                                    [disabled]="!fgStaff.get('facility').value" maxlength="10"
                                    (keypress)="$event.charCode >= 48 && $event.charCode <= 57">
                                <mat-icon matSuffix class="secondary-text">phone</mat-icon>
                                <mat-error *ngIf="fgStaff.get('phoneNumber').errors?.required">Phone number is
                                    required</mat-error>
                                <mat-error *ngIf="fgStaff.get('phoneNumber').errors?.pattern"> Phone number is not
                                    valid.</mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                    <!-- Row 4: Role & Date of Joining -->
                    <div fxLayout="row" fxLayoutAlign="start" fxFlex="1 0 shrink" fxLayoutGap="16px">
                        <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                            <mat-label>Select Role</mat-label>
                            <mat-select required formControlName="roleId" [disabled]="!fgStaff.get('facility').value">
                                <mat-option *ngFor="let role of roleList" [value]="role.code">
                                    {{role.value}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                        <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                            <mat-label>Date of Joining</mat-label>
                            <input matInput required [matDatepicker]="dojPicker" formControlName="dateOfJoining"
                                [disabled]="!fgStaff.get('facility').value">
                            <mat-datepicker-toggle matSuffix [for]="dojPicker"></mat-datepicker-toggle>
                            <mat-datepicker #dojPicker></mat-datepicker>
                            <mat-error *ngIf="fgStaff.get('phoneNumber').errors?.required">Date of Joining is
                                required</mat-error>
                        </mat-form-field>
                    </div>
                    <!-- Row 5: Nationality & Postal Code -->
                    <div fxLayout="row" fxLayoutAlign="start" fxFlex="1 0 shrink" fxLayoutGap="16px">
                        <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                            <mat-label>Nationality</mat-label>
                            <input matInput formControlName="nationality" [disabled]="!fgStaff.get('facility').value">
                        </mat-form-field>
                        <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                            <mat-label>Residential Address Postal Code</mat-label>
                            <input matInput formControlName="postalCode" [disabled]="!fgStaff.get('facility').value"
                                maxlength="6" (keypress)="$event.charCode >= 48 && $event.charCode <= 57">
                        </mat-form-field>
                    </div>
                    <!-- Row 6: Staff Photo (full row) -->
                    <div fxLayout="row" fxLayoutAlign="start" fxFlex="1 0 shrink">
                        <mat-form-field appearance="outline" fxFlex="50" class="pr-4">
                            <mat-label>Staff Photo</mat-label>
                            <input matInput readonly="readonly" formControlName="photo">
                            <button mat-stroked-button color="primary" (click)="uploadPhoto()" matSuffix
                                class="mat-small">Upload</button>
                            <div *ngIf="photoPreview" class="photo-preview-container">
                                <img [src]="photoPreview || 'assets/images/avatars/profile.png'" class="photo-preview"
                                    matTooltip="Staff Photo Preview">
                            </div>
                        </mat-form-field>
                        <div fxFlex="50" fxLayoutAlign="start left" style="padding-left: 10px;">
                            <mat-radio-group formControlName="gender" fxLayout="row" fxLayoutGap="20px">
                                <mat-radio-button value="Male">Male</mat-radio-button>
                                <mat-radio-button value="Female">Female</mat-radio-button>
                            </mat-radio-group>
                        </div>
                    </div>

                    <!-- Row 7: Profile Locked & Buttons -->
                    <div fxLayout="row" fxLayoutAlign="start" fxFlex="1 0 shrink">
                        <div fxFlex="100" fxLayout="column">
                            <div fxLayout="row" fxFlex="100" fxLayoutAlign="start left"
                                style="gap: 10px; align-items: center;">
                                <span style="font-size: 18px;">Do we have to lock the profile from further
                                    change?</span>
                                <mat-checkbox formControlName="isProfileLocked"></mat-checkbox>
                            </div>
                        </div>
                    </div>

                    <div fxLayout="row" fxLayoutAlign="start" fxFlex="1 0 shrink" style="padding-top: 25px;">
                        <div fxFlex="20" fxLayoutAlign="start left">
                            <mat-checkbox formControlName="isActive">Active</mat-checkbox>
                        </div>
                        <div fxFlex="80" fxLayout="column">
                            <div fxLayout="row" fxLayoutAlign="end center" fxFlex="1 0 auto"
                                style="gap: 10px; align-items: center;">
                                <button mat-stroked-button type="reset" (click)="resetExtraDetails()"
                                    color="primary">Reset</button>
                                <button mat-flat-button type="submit" [disabled]="fgStaff.invalid" color="primary">Save
                                    Changes</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div fxFlex="55" fxLayout="column">
                <div class="mat-card mat-elevation-z4 p-24">
                    <mat-form-field style="width:50%;">
                        <button mat-button matPrefix mat-icon-button>
                            <mat-icon>search</mat-icon>
                        </button>
                        <input matInput type="text" (keyup)="getAllStaffs()" [(ngModel)]="search_staff"
                            placeholder="Filter Staff">
                        <button *ngIf="search_staff" mat-button matSuffix mat-icon-button aria-label="Clear"
                            (click)="search_staff=''; getAllStaffs();">
                            <mat-icon>close</mat-icon>
                        </button>
                    </mat-form-field>
                    <table mat-table [dataSource]="dataSource" matSort>
                        <ng-container matColumnDef="employeeId">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Employee ID</th>
                            <td mat-cell *matCellDef="let row"> {{row.employeeId}} </td>
                        </ng-container>
                        <ng-container matColumnDef="employeeName">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Employee Name</th>
                            <td mat-cell *matCellDef="let row"> {{row.employeeName}} </td>
                        </ng-container>
                        <ng-container matColumnDef="emailAddress">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
                            <td mat-cell *matCellDef="let row"> {{row.emailAddress}} </td>
                        </ng-container>
                        <ng-container matColumnDef="phone">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Contact Number</th>
                            <td mat-cell *matCellDef="let row"> {{row.contactNumber}} </td>
                        </ng-container>
                        <ng-container matColumnDef="photo">
                            <th mat-header-cell *matHeaderCellDef>Image </th>
                            <td mat-cell *matCellDef="let row">
                                <img height="48px" width="48px"
                                    [src]="row.photoPath || 'assets/images/avatars/profile.png'" />
                            </td>
                        </ng-container>
                        <ng-container matColumnDef="roleName">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Role</th>
                            <td mat-cell *matCellDef="let row"> {{row.roleName}} </td>
                        </ng-container>
                        <ng-container matColumnDef="actions">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Actions</th>
                            <td mat-cell *matCellDef="let row">
                                <div fxLayout="row" fxLayoutGap="10px">
                                    <button mat-stroked-button color="primary" (click)="loadStaffDetails(row)" matSuffix
                                        class="mat-sm">
                                        <mat-icon>edit</mat-icon>
                                    </button>
                                    <button mat-stroked-button color="warn" (click)="deleteStaff(row)" matSuffix
                                        class="mat-sm">
                                        <mat-icon>delete</mat-icon>
                                    </button>
                                </div>
                            </td>
                        </ng-container>
                        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                        <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                            [ngClass]="{'highlight': selectedRowIndex == row.staffId}">
                        </tr>
                    </table>
                    <div *ngIf="!dataSource?.data?.length && search_staff" class="noRecordsMessage">
                        <mat-icon>search_off</mat-icon>
                        No matching records found. Please try a different search.
                    </div>
                    <mat-paginator [length]="length" [pageSize]="pageSize" [pageSizeOptions]="pageSizeOptions"
                        (page)="pagerEvent($event)"></mat-paginator>
                </div>
            </div>
        </div>
    </div>
</div>
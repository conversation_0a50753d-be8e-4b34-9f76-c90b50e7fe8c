import { Injectable } from '@angular/core';
import { BaseApiServiceService } from './baseapi.service';
import { Router } from '@angular/router';
import { ApiResponse, ApiRequest } from 'app/dto/common/commonDto';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CountryApiService extends BaseApiServiceService {

  constructor(public http: HttpClient, public router: Router) {
    super();
  }

  getCountries(): Observable<ApiResponse> {
    return this
      .http
      .get<ApiResponse>(`${this.baseUrl}countries`);
  }
}

<h1 mat-dialog-title>{{data.name}}</h1>
<div mat-dialog-content>

    <form [formGroup]="form1"  (ngSubmit)="changepassword()">
        <div class="form-group">
          <label>Current Password</label>
          <input
            formControlName="oldPwd"
            id="oldPwd"
            type="password" 
            class="form-control" />
            <div class="alert alert-danger" *ngIf="oldPwd.touched && form1.invalid && oldPwd.invalid">
                <div *ngIf="oldPwd.errors.required">Please enter current password.</div>
              </div>
        </div>
      
        <div class="form-group">
          <label>New Password</label>
           <input 
            formControlName="newPwd" 
            id="newPwd"
            type="password"  
            class="form-control" />
            <div class="alert alert-danger" *ngIf="newPwd.touched && form1.invalid && newPwd.invalid">
                <div *ngIf="newPwd.errors.required">Please enter new password.</div>
              </div>
        </div>
      
        <div class="form-group">
          <label>Confirm Password</label>
           <input 
            formControlName="confirmPwd" 
            id="confirmPwd"
            type="password"  
            class="form-control" />
            <div class="alert alert-danger" *ngIf="confirmPwd.touched && form1.invalid && confirmPwd.invalid">
                <div *ngIf="confirmPwd.errors.required">Please enter confrim password</div>
              </div>
              <div class="alert alert-danger" *ngIf="confirmPwd.valid && form1.invalid && form1.errors.pwdsDontMatch">
                confrim password do not match.
              </div>
        </div>
      
        <button [disabled]="form1.invalid" class="btn btn-primary">Change Password</button>
      
      </form>

</div>
<!-- <div mat-dialog-actions>
    <button mat-button (click)="onNoClick()">No Thanks</button>
    <button mat-button [mat-dialog-close]="data.animal" cdkFocusInitial>Ok</button>
</div> -->

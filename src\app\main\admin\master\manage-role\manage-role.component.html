<div id="forms" class="page-layout simple fullwidth" fxLayout="column">

    <div class="header accent p-24 h-20" fxLayout="row" fxLayoutAlign="start center">
        <div fxLayout="column" fxLayoutAlign="center start">
            <div fxLayout="row" fxLayoutAlign="start center">
                <h2>Role Management</h2>
            </div>
        </div>
    </div>
    <div class="content p-24">

        <div class="mb-24" fxLayout="row" fxLayoutGap="20px" fxLayoutAlign="space-around start" fxLayout.gt-md="row">

            <div fxFlex="35" fxLayout="column">
                <form class="mat-card mat-elevation-z4 p-24" fxLayout="column" fxLayoutAlign="start"
                    [formGroup]="fgRole" fxFlex="1 0 auto" name="form" (ngSubmit)="saveRole()" #formRole="ngForm">
                    <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                        <mat-form-field appearance="outline" fxFlex="100" class="pr-4">
                            <mat-label>Select Facility</mat-label>
                            <mat-select formControlName="facility" (selectionChange)="onFacilityChange()">
                                <ngx-mat-select-search [formControl]="facilityFilterCtrl" placeholderLabel="Search..."
                                    noEntriesFoundLabel="No results found">
                                </ngx-mat-select-search>
                                <mat-option *ngFor="let facility of filteredFacilityList | async"
                                    [value]="facility.code">
                                    {{facility.value}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                    <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                        <mat-form-field appearance="outline" fxFlex="100" class="pr-4">
                            <mat-label>Role Name</mat-label>
                            <input matInput required formControlName="roleName"
                                [disabled]="!fgRole.get('facility').value">
                            <mat-icon matSuffix class="secondary-text">assignment</mat-icon>
                            <mat-error>Role Name is required!</mat-error>
                        </mat-form-field>
                    </div>
                    <div fxLayout="row" fxLayoutAlign="start " fxFlex="1 0 shrink">
                        <mat-radio-group formControlName="roleType" fxLayout="row" fxLayoutGap="20px">
                            <mat-radio-button value="Ops">Ops</mat-radio-button>
                            <mat-radio-button value="Non-Ops">Non-Ops</mat-radio-button>
                        </mat-radio-group>
                    </div>
                    <div fxLayout="row" class="mt-20" fxLayoutAlign="space-around start" fxLayout.gt-md="row">
                        <div fxFlex="40" fxLayout="column">
                            <div fxLayout="row wrap" fxFlex="100" fxLayoutAlign="start left">
                                <mat-checkbox fxFlex="50" formControlName="active">Active</mat-checkbox>
                            </div>
                        </div>
                        <div fxFlex="60" fxLayout="column">
                            <div fxLayout="row" fxLayoutAlign="end center" fxFlex="1 0 auto">
                                <button mat-stroked-button type="reset" (click)="resetExtraDetails()"
                                    class="mat-top-margin mr-20" color="primary">Reset</button>
                                <button mat-flat-button type="submit" [disabled]="fgRole.invalid"
                                    class="mat-top-margin mr-20" color="primary">Save Changes</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div fxFlex="65" fxLayout="column">
                <div class="mat-card mat-elevation-z4 p-24">
                    <mat-form-field style="width:50%;">
                        <button mat-button matPrefix mat-icon-button>
                            <mat-icon>search</mat-icon>
                        </button>
                        <input matInput type="text" (keyup)="getAllRoles()" [(ngModel)]="search_role"
                            placeholder="Filter Roles">
                        <button *ngIf="search_role" mat-button matSuffix mat-icon-button aria-label="Clear"
                            (click)="search_role=''; getAllRoles();">
                            <mat-icon>close</mat-icon>
                        </button>
                    </mat-form-field>
                    <table mat-table [dataSource]="dataSource" matSort>
                        <ng-container matColumnDef="roleName">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Role Name</th>
                            <td mat-cell *matCellDef="let row"> {{row.roleName}} </td>
                        </ng-container>
                        <ng-container matColumnDef="active">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Active</th>
                            <td mat-cell *matCellDef="let row"> {{row.active===true?'Yes': 'No'}} </td>
                        </ng-container>
                        <ng-container matColumnDef="roleType">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Role Type</th>
                            <td mat-cell *matCellDef="let row"> {{row.roleType}} </td>
                        </ng-container>
                        <ng-container matColumnDef="actions">
                            <th mat-header-cell *matHeaderCellDef mat-sort-header>Actions</th>
                            <td mat-cell *matCellDef="let row">
                                <button mat-stroked-button color="primary" (click)="loadRoleDetails(row)" matSuffix
                                    class="mat-sm">
                                    <mat-icon>edit</mat-icon>
                                </button>

                                <button mat-stroked-button color="warn" (click)="deleteRole(row)" matSuffix
                                    class="mat-sm">
                                    <mat-icon>delete</mat-icon>
                                </button>
                            </td>
                        </ng-container>
                        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                        <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                            [ngClass]="{'highlight': selectedRowIndex == row.roleId}">
                        </tr>
                    </table>
                    <div *ngIf="!dataSource?.data?.length && search_role" class="noRecordsMessage">
                        <mat-icon>search_off</mat-icon>
                        No matching records found. Please try a different search.
                    </div>
                    <mat-paginator [length]="length" [pageSize]="pageSize" [pageSizeOptions]="pageSizeOptions"
                        (page)="pagerEvent($event)"></mat-paginator>
                </div>
            </div>
        </div>
    </div>
</div>
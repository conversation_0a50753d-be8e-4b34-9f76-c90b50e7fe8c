import { Component, ViewChild } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { GridSearchRequestDto } from 'app/dto/common/gridDto';
import { CompanyResponseDto } from 'app/dto/companyDto';
import { mstCompanyModule, SaveCompanyRequestDto } from 'app/dto/saveCompanyDto';
import { BaseComponent } from 'app/main/common/base.component';
import { CompanyApiService } from 'app/services/api/company.api.service';
import { CountryApiService } from 'app/services/api/country.api.service';
import { environment } from 'environments/environment';
import { Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import * as moment from 'moment';
import { CountryList, ModuleList } from 'app/dto/countryDto';
import { ModuleApiService } from 'app/services/api/modules.api.service';
import { mstUserApp } from 'app/dto/saveUserDto';
import { FileuploaderComponent } from 'app/main/common/fileuploader/fileuploader.component';
import { userIDDto } from 'app/dto/userDto';

@Component({
  selector: 'app-manage-company',
  templateUrl: './managecompany.component.html',
  styleUrls: ['./managecompany.component.scss']
})

export class ManagecompanyComponent extends BaseComponent {
  @ViewChild('formUser', { static: true }) ngForm;
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  search_user = '';
  search_userEmail = '';
  tkn = localStorage.getItem('SSOTOKEN');
  fgUser = new FormGroup({
    id: new FormControl(''),
    companyId: new FormControl(''),
    companyName: new FormControl(''),
    effectiveDate: new FormControl(""),
    contractExpiryDate: new FormControl(""),
    countryControl: new FormControl(''),
    moduleDesc: new FormControl(''),
    active: new FormControl(true),
    logo: new FormControl(''),
    countryCode: new FormControl(""),
    smsUrl: new FormControl(""),
  });
  selectedRowIndex = 0;
  countryFilteredOptions: Observable<CountryList[]> | undefined;
  isExistingUpdateMode = false;
  displayedColumns: string[] = ['companyName', 'effectiveDate', 'country', 'companyLogo', 'contractExpiryDate', 'moduleInst', 'active', 'edit'];
  dataSource: MatTableDataSource<CompanyResponseDto>;
  companyGridData: CompanyResponseDto[] = [];
  countryList: CountryList[];
  moduleList: ModuleList[];
  checkedOptions: ModuleList[];
  myControl = new FormControl();
  length = 50;
  pageSize = 5;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageEvent: PageEvent;

  constructor(private _countryApi: CountryApiService, private _companyApi: CompanyApiService,
    _snackBar: MatSnackBar, public _dialog: MatDialog, private _router: Router, private _moduleApi: ModuleApiService) {
    super(_snackBar);
  }

  ngOnInit() {
    const isAd = localStorage.getItem('SSOADMIN');
    if (isAd == '1' && this.tkn.trim().length > 0) {
    } else {
      localStorage.setItem('SSOUSERNAME', '');
      localStorage.setItem('SSOUSERPIC', '');
      this._router.navigateByUrl('/login');
    }

    // this.paginator.pageSize = 5;
    // this.paginator.pageIndex = 1;
    this.getAllCountries();
    this.getAllCompanies();
    this.getAllModules();
  }

  getAllModules(): void {
    this.countryList = [];
    this.search_userEmail = localStorage.getItem('SSOUSEREMAIL');
    const gridDataReq: userIDDto = {
      id: "0",
      UserEmail: this.search_userEmail
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._moduleApi.getModules(obj)
      .subscribe(
        (data: any) => {
          this.moduleList = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray) {
            const _c: ModuleList = {
              moduleId: _obj.moduleId,
              moduleDesc: _obj.moduleDesc,
              URL: _obj.URL,
              Description: ''
            };
            this.moduleList.push(_c);
          }
        },
        _error => {
        }
      );
  }

  getAllCountries(): void {
    this.countryList = [];
    this._countryApi.getCountries()
      .subscribe(
        (data: any) => {
          this.countryList = [];
          const objArray = JSON.parse(data.obj.toString());
          for (const _obj of objArray) {
            const _c: CountryList = {
              countryId: _obj.countryId,
              countryName: _obj.countryName
            };
            this.countryList.push(_c);
          }
          this.countryFilteredOptions = this.fgUser.controls.countryControl.valueChanges.pipe(
            startWith(''),
            map(value => this._filter(value))
          );
        },
        _error => {
        }
      );
  }

  private _filter(value: string): CountryList[] {
    const filterValue = value.toLowerCase();
    return this.countryList.filter(country =>
      country.countryName.toLowerCase().startsWith(filterValue));
  }

  uploadLogo(): boolean {
    const dialogRef = this._dialog.open(FileuploaderComponent, {
      width: '400px',
      data: {
        name: 'Change Facility Logo',
        filePath: '',
        base64: this.fgUser.controls['logo'].value
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      console.log(result.data);
      if (result.data && result.data.length > 0) {
        this.fgUser.controls.logo.setValue(result.data);
      }
      else {
        this.fgUser.controls.logo.setValue(null);
      }
    });
    return false;
  }

  saveCompany(): void {
    //console.log(this.fgUser);
    const companyObj: SaveCompanyRequestDto = {
      isActive: (this.fgUser.controls.active.value == '' || this.fgUser.controls.active.value == null) ? false : this.fgUser.controls.active.value,
      companyName: this.fgUser.controls.companyName.value,
      companyId: (this.fgUser.controls.id.value == '' || this.fgUser.controls.id.value == null) ? 0 : this.fgUser.controls.id.value,
      effectiveDate: this.pareseDateTime(this.fgUser.controls.effectiveDate.value),
      contractExpiryDate: this.pareseDateTime(this.fgUser.controls.contractExpiryDate.value),
      country: this.fgUser.controls.countryControl.value,
      companyLogo: this.fgUser.controls.logo.value,
      moduleInst: [],
      countryCode: this.fgUser.controls.countryCode.value,
      smsUrl: this.fgUser.controls.smsUrl.value,
      createdby: Number(localStorage.getItem('SSOUSERID'))
    };

    // facility mapp
    this.checkedOptions.forEach(module => {
      const moduleCompInst: mstCompanyModule = {
        companyId: companyObj.companyId,
        moduleId: module.moduleId
      };
      companyObj.moduleInst.push(moduleCompInst);
    });

    const obj = this.wrapRequestObject(companyObj);
    this._companyApi.saveCompany(obj)
      .subscribe(
        (data) => {
          if (data.code === 1) {
            this.showSuccessMsg('Changes has been saved successfully');
            this.ngForm.resetForm();
            this.selectedRowIndex = 0;
            this.getAllCountries();
            this.getAllCompanies();
            this.getAllModules();
          } else {
            this.showErrorMsg(data.msg);
          }
        },
        _error => {
        }
      );

  }

  getAllCompanies(): void {
    this.companyGridData = [];
    const gridDataReq: GridSearchRequestDto = {
      page: 0,
      pageSize: this.pageSize,
      searchString: this.search_user,
      UserEmail: ""
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._companyApi.getAllCompanies(obj)
      .subscribe(
        (data) => {
          this.companyGridData = [];
          const responseData = JSON.parse(data.obj.toString());
          this.length = responseData ? responseData.totalRecords : 0;
          if (responseData && responseData['gridData']) {
            for (const _obj of responseData['gridData']) {
              const _u: CompanyResponseDto = {
                companyId: _obj.companyId,
                companyName: _obj.companyName,
                effectiveDate: _obj.effectiveDate,
                country: _obj.country,
                contractExpiryDate: _obj.contractExpiryDate,
                active: _obj.isActive,
                moduleInst: _obj.ModuleInsts,
                companyLogo: _obj.companyLogo,
                countryCode: _obj.countryCode,
                smsUrl: _obj.smsUrl
              };
              this.companyGridData.push(_u);
            }
          }
          this.dataSource = new MatTableDataSource(this.companyGridData);
          this.dataSource.sort = this.sort;
          this.paginator.firstPage();
        },
        _error => {
        }
      );
  }

  pagerEvent(event?: PageEvent): void {
    const gridDataReq: GridSearchRequestDto = {
      page: event.pageIndex,
      pageSize: event.pageSize,
      searchString: this.search_user,
      UserEmail: ""
    };
    const obj = this.wrapRequestObject(gridDataReq);
    this._companyApi.getAllCompanies(obj)
      .subscribe(
        (data) => {
          this.companyGridData = [];
          const responseData = JSON.parse(data.obj.toString());
          for (const _obj of responseData['gridData']) {
            const _u: CompanyResponseDto = {
              companyId: _obj.companyId,
              companyName: _obj.companyName,
              effectiveDate: _obj.effectiveDate,
              country: _obj.country,
              contractExpiryDate: _obj.contractExpiryDate,
              active: _obj.isActive,
              moduleInst: _obj.ModuleInsts,
              companyLogo: _obj.companyLogo,
              smsUrl: _obj.smsUrl,
              countryCode: _obj.countryCode,
            };
            this.companyGridData.push(_u);
          }
          this.dataSource = new MatTableDataSource(this.companyGridData);
          this.dataSource.sort = this.sort;
        },
        _error => {
        }
      );
  }

  pareseDateTime(date) {
    //resolving the utc time issues
    return moment(date).add(moment(date).utcOffset(), 'm').utc();
  }

  resetExtraDetails(): void {
    this.search_user = '';
    this.selectedRowIndex = 0;
    this.isExistingUpdateMode = false;
    this.getAllCountries();
  }

  loadCompanyDetails(row) {
    this.isExistingUpdateMode = true;
    this.selectedRowIndex = row.companyId;
    this.fgUser.controls.id.setValue(row.companyId);
    this.fgUser.controls.companyId.setValue(row.companyId);
    this.fgUser.controls.companyName.setValue(row.companyName);
    this.fgUser.controls.effectiveDate.setValue(row.effectiveDate);
    this.fgUser.controls.contractExpiryDate.setValue(row.contractExpiryDate);
    this.fgUser.controls.countryControl.setValue(row.country);
    this.fgUser.controls.active.setValue(row.active);
    this.getAssignedModules(row.moduleInst);
    this.fgUser.controls.logo.setValue(row.companyLogo);
    this.fgUser.controls.countryCode.setValue(row.countryCode);
    this.fgUser.controls.smsUrl.setValue(row.smsUrl);
    // this.fgUser.setValue({
    //   id: row.companyId,
    //   companyId: row.companyId,
    //   companyName: row.companyName, 
    //   effectiveDate: new Date(row.effectiveDate),
    //   contractExpiryDate: new Date(row.contractExpiryDate),
    //   countryControl: row.country,
    //   active: row.isActive,
    //   moduleDesc: 
    // });
  }

  getModuleDesc(moduleDescArray) {
    if (moduleDescArray && moduleDescArray.length > 0) {
      const moduleIdCollection = moduleDescArray.map(module => module.ModuleId);
      if (this.moduleList) {
        return this.moduleList.filter(mod => moduleIdCollection.includes(mod.moduleId)).map(mod => mod.moduleDesc).toString();
      }
      else {
        return "";
      }
    } else {
      return "";
    }
  }

  getAssignedModules(moduleDescArray) {
    const moduleIdCollection = moduleDescArray.map(module => module.ModuleId);
    this.checkedOptions = this.moduleList.filter(mod => moduleIdCollection.includes(mod.moduleId));
  }

  change(event: PageEvent) {
    this.getAllCompanies();
  }

  navigateToHousekeeping(): void {
    window.location.href = environment.hpbsUrl + '#/login?token=' + this.tkn;
  }

}

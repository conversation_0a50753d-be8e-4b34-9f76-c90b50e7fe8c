import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
    selector: 'app-confirm-dialog',
    template: `
        <div class="p-24">
            <h2 mat-dialog-title>Confirm Delete</h2>
            <mat-dialog-content>
                <p>Are you sure you want to delete this staff member?</p>
            </mat-dialog-content>
            <mat-dialog-actions align="end">
                <button mat-button type="button" (click)="onCancel()">Cancel</button>
                <button mat-flat-button color="warn" (click)="onConfirm()">Delete</button>
            </mat-dialog-actions>
        </div>
    `,
    styles: [`
        mat-dialog-content {
            min-width: 300px;
        }
        .p-24 {
            padding: 24px;
        }
    `]
})
export class ConfirmDialogComponent {
    constructor(
        public dialogRef: MatDialogRef<ConfirmDialogComponent>,
        @Inject(MAT_DIALOG_DATA) public data: any
    ) {}

    onConfirm(): void {
        this.dialogRef.close(true);
    }

    onCancel(): void {
        this.dialogRef.close(false);
    }
} 
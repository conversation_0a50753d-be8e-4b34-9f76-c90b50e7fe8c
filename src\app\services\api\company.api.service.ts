import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ApiRequest, ApiResponse } from 'app/dto/common/commonDto';
import { Observable } from 'rxjs';
import { BaseApiServiceService } from './baseapi.service';


@Injectable({
  providedIn: 'root'
})

export class CompanyApiService extends BaseApiServiceService {

  constructor(public http: HttpClient, public router: Router) {
    super();
  }

  getCompanies(request: ApiRequest): Observable<ApiResponse> {
    return this
    .http
    .post<ApiResponse>(`${this.baseUrl}companieslist`,
      {
        'data': request
      });
  }

  getAllCompanies(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}companies`,
        {
          'data': request
        });
  }

  saveCompany(request: ApiRequest): Observable<ApiResponse> {
    return this
      .http
      .post<ApiResponse>(`${this.baseUrl}savecompany`,
        {
          'data': request
        });
  }  

}

<div id="forms" class="page-layout simple fullwidth" fxLayout="column">
    <!-- <div class="header p-20 h-20" fxLayout="row" fxLayoutAlign="start center">
        <div fxLayout="column" fxLayoutAlign="center start">
            <div fxLayout="row" fxLayoutAlign="start center">
                <h2>Dashboard</h2>
            </div>
        </div>
    </div> -->
    <div style="display: flex; align-items: center; margin: 40px 30px; justify-content: start; gap: 10px;">
        <div class="avatar-wrapper">
            <img class="avatar" [src]="picUrl" (error)="onImageError()" (load)="onImageLoad()"
                [style.display]="showImage ? 'block' : 'none'">
            <div class="avatar-initials" [style.display]="showImage ? 'none' : 'flex'" [title]="userName">
                {{getInitials()}}
            </div>
        </div>
        <div style="font-size: 32px; font-weight: 900; color: #002D68;">Welcome back, {{userName}}!</div>
    </div>
    <div class="content p-28" style="background-color: #F1F5F9;">
        <mat-grid-list cols="2" gutterSize="10px" rowHeight="200px">
            <mat-grid-tile *ngFor="let module of moduleList" class="grid">
                <label class="Header">{{module.moduleDesc}}</label>
                <p class="pheader">{{module.Description}}</p>
                <div class="divarrow" (click)="navigateToHousekeeping(module.URL)"><label
                        class="labletext">Open</label><mat-icon class="rightarrow">play_circle_filled</mat-icon></div>
                <img src='{{ "assets/images/logos/" + module.moduleDesc + ".png" }}' class="img"
                    (click)="navigateToHousekeeping(module.URL)" />
            </mat-grid-tile>
        </mat-grid-list>
    </div>
</div>
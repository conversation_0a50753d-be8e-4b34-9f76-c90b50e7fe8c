import { mstUserInstance, mstUserApp, mstUserModule } from './saveUserDto';

export class UsersResponseDto {
    // export class UserResponseDto {
    userId: number;
    userName: string;
    userFullName: string;
    email: string;
    isActive: boolean;
    isExternalLogin: boolean;
    userInsts: mstUserInstance[];
    userApps: mstUserApp[];
    userModules: mstUserModule[];
    isAdmin: boolean;
    Edit: number;
}

// export class UserResponseDto {
//     userId: number;
//     userName: string;
//     userFullName: string;
//     email: string;
//     isActive: boolean;
//     isExternalLogin: boolean;
//     userInsts: mstUserInstance[];
//     userApps: mstUserApp[];
// }

export class UserGridData {
    userId: number;
    userName: string;
    roleId: number;
    active: boolean;
}

export class RoleResponseDto {
    roleId: number;
    roleName: string;
    roleType: string;
    active: boolean;
    facility: string | null;
}

export class SaveRoleRequestDto {
    roleId: number;
    roleName: string;
    active: boolean;
    roleType: string;
    facilityId: number;
    createdBy: number;
}

export class UserRoleRequestDto {
    system: string;
}



export class UserLoginDto {
    email: string;
    password: string;
}




export class ExternalLoginRequestDto {
    id: string;
    name: string;
    email: string;
    photoUrl: string;
    firstName: string;
    lastName: string;
    authToken: string;
    idToken: string;
    provider: string;
}

export class userIDDto {
    id: string;
    UserEmail: string;
}
